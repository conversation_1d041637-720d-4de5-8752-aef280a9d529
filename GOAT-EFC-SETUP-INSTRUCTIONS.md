# GOAT EFC Website Setup Instructions

## Overzicht
Deze setup bevat een complete WordPress thema uitbreiding voor GOAT EFC met:
- 3 Custom Post Types: Partners, Events, Team Members
- 14 ACF Blokken voor verschillende content secties
- Complete styling en JavaScript functionaliteit
- Single page templates voor Events en Team Members

## 1. Custom Post Types
De volgende custom post types zijn toegevoegd:
- **Partners** (`/partners`) - Voor sponsoren en partners
- **Events** (`/events`) - Voor evenementen en toernooien  
- **Team Members** (`/team`) - Voor teamleden met sociale media links

## 2. ACF Blokken
De volgende blokken zijn beschik<PERSON>ar in de Gutenberg editor:

### Home Blokken
- **GOAT Home Header Block** - Hero sectie met video/afbeelding achtergrond
- **GOAT Home Intro Block** - Intro sectie met features
- **GOAT Team Members Home Block** - Featured team members

### Content Blokken  
- **GOAT Big Image Slider Block** - Grote afbeelding slider
- **GOAT Partners Team Events Block** - Gecombineerde sectie
- **GOAT Instagram Embed Block** - Instagram feed integratie
- **GOAT Header Block** - Algemene header voor pagina's
- **GOAT Partners Block** - Partners overzicht
- **GOAT Three Events Block** - 3 featured events
- **GOAT Events Block** - Alle events met filters
- **GOAT Text Two Column Block** - Twee kolommen tekst
- **GOAT Steps Block** - Stappen/proces weergave
- **GOAT Contact Block** - Contact informatie en formulier
- **GOAT Team Overview Block** - Volledig team overzicht

## 3. Installatie Stappen

### Stap 1: ACF Field Groups Importeren
1. Ga naar WordPress Admin > Custom Fields > Tools
2. Klik op "Import Field Groups"
3. Upload `acf-export-goat-efc.json`
4. Upload `acf-export-goat-efc-part2.json`
5. Klik op "Import"

### Stap 2: Permalinks Vernieuwen
1. Ga naar Settings > Permalinks
2. Klik op "Save Changes" (geen wijzigingen nodig)
3. Dit activeert de nieuwe custom post types URLs

### Stap 3: JavaScript Bestanden Registreren
Voeg deze regels toe aan je `functions.php` in de `$blocks` array (rond regel 28):

```php
'GOAT BIG IMAGE SLIDER' => '/blocks/js/goat-big-image-slider-block.js',
'GOAT TEAM OVERVIEW' => '/blocks/js/goat-team-overview-block.js',
```

### Stap 4: LESS/CSS Compileren
Compileer de nieuwe LESS bestanden:
- `blocks/less/goat-home-header-block.less`
- `blocks/less/goat-team-members-home-block.less`
- `blocks/less/goat-partners-block.less`

## 4. Demo Content Toevoegen

### Partners Toevoegen
1. Ga naar Partners > Add New
2. Voeg titel toe (bijv. "EA Sports")
3. Upload Partner Logo
4. Voeg Partner Website URL toe
5. Voeg Partner Description toe
6. Publiceer

**Voorbeelden:**
- EA Sports (https://ea.com)
- PlayStation (https://playstation.com)
- Twitch (https://twitch.tv)
- Discord (https://discord.com)

### Events Toevoegen
1. Ga naar Events > Add New
2. Voeg titel toe (bijv. "GOAT Cup 2024")
3. Stel Event Date in
4. Voeg Event Location toe
5. Upload Event Images (gallery)
6. Voeg Event Description toe
7. Voeg Registration Link toe
8. Publiceer

**Voorbeelden:**
- GOAT Cup 2024 - Datum: 2024-08-15, Locatie: Amsterdam
- Weekly Tournament - Datum: 2024-07-20, Locatie: Online
- Community Meetup - Datum: 2024-09-01, Locatie: Rotterdam

### Team Members Toevoegen
1. Ga naar Team Members > Add New
2. Voeg naam toe (bijv. "John Doe")
3. Upload featured image (profielfoto)
4. Vul Role/Position in
5. Voeg Bio toe
6. Selecteer Member Category
7. Vul sociale media URLs in:
   - Twitch URL
   - Instagram URL
   - Twitter URL
   - Discord URL
   - etc.
8. Stel "Show Detail Page" in (aan/uit)
9. Upload Additional Images (optioneel)
10. Voeg Achievements toe (optioneel)
11. Publiceer

**Voorbeelden:**
- Captain: John "GoatKing" Smith - Management
- Player: Mike "FastFingers" Johnson - Players  
- Streamer: Sarah "StreamQueen" Davis - Content Creators

## 5. Pagina's Aanmaken

### Home Pagina
Maak een nieuwe pagina en voeg deze blokken toe:
1. GOAT Home Header Block
2. GOAT Home Intro Block
3. GOAT Team Members Home Block
4. GOAT Partners Team Events Block
5. GOAT Instagram Embed Block

### Team Pagina
Maak een pagina "Team" en voeg toe:
1. GOAT Header Block
2. GOAT Team Overview Block

### Events Pagina  
Maak een pagina "Events" en voeg toe:
1. GOAT Header Block
2. GOAT Events Block

### Partners Pagina
Maak een pagina "Partners" en voeg toe:
1. GOAT Header Block
2. GOAT Partners Block

### Contact Pagina
Maak een pagina "Contact" en voeg toe:
1. GOAT Header Block
2. GOAT Contact Block

## 6. Styling Aanpassingen
De LESS bestanden bevatten basis styling. Pas de kleuren aan naar je brand:
- Primaire kleur: `#ff6b35` (oranje)
- Secundaire kleur: `#f7931e` (geel-oranje)
- Tekst kleur: `#333`
- Achtergrond: `#f8f9fa`

## 7. JavaScript Functionaliteit
- **Image Slider**: Auto-play, touch/swipe support, keyboard navigation
- **Team Filters**: Filter team members op categorie
- **Load More**: AJAX laden van meer content (optioneel)

## 8. Troubleshooting

### ACF Blokken Verschijnen Niet
- Controleer of ACF Pro geïnstalleerd is
- Vernieuw permalinks
- Check of field groups correct geïmporteerd zijn

### Styling Werkt Niet
- Compileer LESS bestanden naar CSS
- Voeg CSS toe aan je main stylesheet
- Check browser cache

### Custom Post Types Niet Zichtbaar
- Ga naar Settings > Permalinks en klik Save
- Check of functions.php correct is bijgewerkt

## 9. Volgende Stappen
Na setup kun je:
- Meer team members toevoegen
- Events plannen en toevoegen
- Partners uitbreiden
- Styling verder aanpassen
- Extra functionaliteit toevoegen

Voor vragen of problemen, check de WordPress admin en browser console voor error messages.
