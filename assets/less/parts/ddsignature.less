@import '../vw_values.less';
@import '../constants.less';

.signatureDD {
    display: inline-block;
    vertical-align: middle;
    cursor: pointer;
    &:hover { 
        opacity: .4;
        transition: opacity .3s;
    }
    .linkDD {
        color: #000000;
        cursor: pointer;
        font-size: @vw16;
        display: inline-block;
        text-decoration: none;
    }
    .innerTextDD, .svgWrap {
        cursor: pointer;
        display: inline-block;
        vertical-align: middle;
    }
    .svgWrap { 
        cursor: pointer;
        display: inline-block;
        vertical-align: middle;
        width: @vw80;
        height: auto;
        svg {
            cursor: pointer;
            display: inline-block;
            vertical-align: middle;
            width: 100%;
            height: auto;
            object-fit: contain;
        }
    }
}

@media all and (max-width: 1160px) {
    .signatureDD {
        .linkDD {
            font-size: @vw16-1160;
        }
        .svgWrap { 
            width: @vw80-1160;
        }
    }
}

@media all and (max-width: 580px) {
    .signatureDD {
        .linkDD {
            font-size: @vw24-580;
        }
        .svgWrap { 
            width: @vw120-580 + @vw10-580;
        }
    }
}