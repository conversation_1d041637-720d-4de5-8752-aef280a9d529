body.touch .footer .bigTitle a:hover {
  padding-left: 0;
  padding-right: 7.758vw;
  color: #191919 !important;
}
body.touch .footer .bigTitle a:hover:after {
  width: 0%;
}
body.touch .footer .bigTitle a:hover i {
  opacity: 0;
}
.footer {
  background: #C2C2B5;
  padding: 4.051vw 0 1.157vw 0;
  position: relative;
  overflow: hidden;
}
.footer .backgroundWrapper {
  width: 60vw;
  position: absolute;
  height: 60vw;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  top: 50%;
  opacity: 1;
  left: 0;
}
.footer .backgroundWrapper .background {
  opacity: 1;
  position: absolute;
  animation: moveBackground 10s infinite ease-in-out alternate;
  top: 0;
  left: 0;
  width: 100%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  height: 100%;
  background: #FFFFFF;
  -webkit-mask-image: radial-gradient(#000000, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0));
  mask-image: radial-gradient(#000000, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0));
}
.footer .bottomFooter {
  color: #191919;
  margin-top: 1.273vw;
}
.footer .bottomFooter .col {
  display: inline-block;
  vertical-align: middle;
  width: 50%;
}
.footer .bottomFooter .col:last-child {
  text-align: right;
}
.footer .logo {
  width: 1.389vw;
  display: inline-block;
  vertical-align: middle;
}
.footer .logo svg {
  object-fit: contain;
  width: 100%;
  height: auto;
}
.footer .innerMenu {
  display: inline-block;
  margin-left: 4.63vw;
  vertical-align: middle;
  list-style: none;
}
.footer .innerMenu li {
  display: inline-block;
}
.footer .innerMenu a {
  display: inline-block;
  vertical-align: middle;
  padding: 0.579vw;
  cursor: pointer;
  color: #191919;
  text-decoration: none;
  transition: color 0.3s, transform 0.3s;
}
.footer .innerMenu a:not(:last-of-type) {
  margin-right: 1.273vw;
}
.footer .innerMenu a:hover {
  color: #DC461E;
}
@media all and (max-width: 1160px) {
  .footer {
    padding: 6.034vw 0 1.724vw 0;
  }
  .footer .cols {
    margin-left: -1.724vw;
    width: calc(100% + 3.448vw);
  }
  .footer .cols .col {
    margin: 0 1.724vw;
    width: calc(33.3333% - 3.448vw);
  }
  .footer img {
    width: 12.931vw;
    margin-top: 3.448vw;
  }
  .footer .bigTitle a {
    padding-right: 3.879vw;
    padding-bottom: 0.862vw;
    padding-left: 0;
  }
  .footer .bigTitle a:after {
    height: 2px;
  }
  .footer .bigTitle a:hover {
    padding-left: 3.879vw;
  }
  .footer .bigTitle a i {
    left: -2.586vw;
  }
  .footer .logo {
    width: 2.069vw;
  }
  .footer .innerMenu {
    margin-left: 6.897vw;
  }
  .footer .innerMenu a {
    padding: 0.862vw;
  }
  .footer .innerMenu a:not(:last-of-type) {
    margin-right: 1.897vw;
  }
  .footer .bottomFooter {
    margin-top: 1.897vw;
  }
}
@media all and (max-width: 580px) {
  .footer {
    padding: 12.069vw 0 3.448vw 0;
  }
  .footer .backgroundWrapper {
    width: 120vw;
    height: 120vw;
  }
  .footer .cols {
    margin-left: -3.448vw;
    width: calc(100% + 6.897vw);
  }
  .footer .cols .col {
    margin: 0 3.448vw;
    width: calc(50% - 6.897vw);
  }
  .footer .cols .col:nth-child(2) {
    display: none;
  }
  .footer img {
    width: 25.861vw;
    margin-top: 6.897vw;
  }
  .footer .bigTitle a {
    padding-right: 7.758vw;
    padding-bottom: 1.724vw;
    padding-left: 0;
  }
  .footer .bigTitle a:after {
    height: 2px;
  }
  .footer .bigTitle a:hover {
    padding-left: 7.758vw;
  }
  .footer .bigTitle a i {
    left: -5.172vw;
  }
  .footer .logo {
    width: 4.137vw;
  }
  .footer .bottomFooter {
    margin-top: 8.62vw;
    font-size: 3.793vw;
  }
  .footer .innerMenu {
    display: none;
  }
}
@keyframes moveBackground {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(10vw, -5vw);
  }
  50% {
    transform: translate(-10vw, 5vw);
  }
  75% {
    transform: translate(5vw, -10vw);
  }
  100% {
    transform: translate(0, 0);
  }
}
