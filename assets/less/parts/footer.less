@import '../vw_values.less';
@import '../constants.less';

body {
  &.touch {
    .footer {
      .bigTitle {
        a {
          &:hover {
            padding-left: 0;
            padding-right: @vw45-580;
            color: @hardBlack !important;
            &:after {
                width: 0%;
            }
            i {
                opacity: 0;
            } 
        } 
        }
      }
    }
  }
}

.footer {
    background: @almostWhite;
    padding: @vw70 0 @vw20 0;
    position: relative;
    overflow: hidden;
    .backgroundWrapper { 
      width: 60vw;
      position: absolute;
      height: 60vw;
      .transform(translateY(-50%));
      top: 50%;
      opacity: 1;
      left: 0;
      .background {
        opacity: 1;
        position: absolute;
        animation: moveBackground 10s infinite ease-in-out alternate;
        top: 0;
        left: 0;
        width: 100%;
        .rounded(50%);
        height: 100%; 
        background: @hardWhite;
        -webkit-mask-image: radial-gradient(rgba(0,0,0,1), rgba(0,0,0,0), rgba(0,0,0,0));
        mask-image: radial-gradient(rgba(0,0,0,1), rgba(0,0,0,0), rgba(0,0,0,0));
      }
    }
    .bottomFooter {
        color: @hardBlack;
        margin-top: @vw22;
        .col {
          display: inline-block;
          vertical-align: middle;
          width: 50%;
          &:last-child {
            text-align: right;
          }
        }
    }
    .logo {
      width: @vw24;
      display: inline-block;
      vertical-align: middle;
      svg {
        object-fit: contain;
        width: 100%;
        height: auto;
      }
    }
    .innerMenu {
      display: inline-block;
      margin-left: @vw80;
      vertical-align: middle;
      list-style: none;
      li {
        display: inline-block;
      }
      a {
          display: inline-block;
          vertical-align: middle;
          padding: @vw10;
          cursor: pointer;
          color: @almostBlack;
          text-decoration: none;
          transition: color .3s, transform .3s;
          &:not(:last-of-type) {
              margin-right: @vw22;
          }
          &:hover {
              color: @primaryColor;
          }
      }
  }
}

@media all and (max-width: 1160px) {
    .footer {
      padding: @vw70-1160 0 @vw20-1160 0;
      .cols {
        margin-left: -@vw20-1160;
        width: calc(100% + @vw40-1160);
        .col {
          margin: 0 @vw20-1160;
          width: calc(33.3333% - @vw40-1160);
        }
      }
      img {
        width: @vw100-1160 + @vw50-1160;
        margin-top: @vw40-1160;
      }
      .bigTitle {
        a {
          padding-right: @vw45-1160;
          padding-bottom: @vw10-1160;
          padding-left: 0;
          &:after {
            height: 2px;
          }
          &:hover {
            padding-left: @vw45-1160;
          }
          i {
            left: -@vw30-1160;
          }
        }
      }
      .logo {
        width: @vw24-1160;
      }
      .innerMenu {
        margin-left: @vw80-1160;
        a {
            padding: @vw10-1160;
            &:not(:last-of-type) {
                margin-right: @vw22-1160;
            }
        }
    }
      .bottomFooter {
        margin-top: @vw22-1160;
      }
    }
  }
  
  @media all and (max-width: 580px) {
    .footer {
      padding: @vw70-580 0 @vw20-580 0;
      .backgroundWrapper { 
        width: 120vw;
        height: 120vw;
      }
      .cols {
        margin-left: -@vw20-580;
        width: calc(100% + @vw40-580);
        .col {
          margin: 0 @vw20-580;
          width: calc(50% - @vw40-580);
          &:nth-child(2) {
            display: none;
          }
        }
      }
      img {
        width: @vw100-580 + @vw50-580;
        margin-top: @vw40-580;
      }
      .bigTitle {
        a {
          padding-right: @vw45-580;
          padding-bottom: @vw10-580;
          padding-left: 0;
          &:after {
            height: 2px;
          }
          &:hover {
            padding-left: @vw45-580;
          }
          i {
            left: -@vw30-580;
          }
        }
      }
      .logo {
        width: @vw24-580;
      }
      .bottomFooter {
        margin-top: @vw50-580;
        font-size: @vw22-580;
      }
      .innerMenu {
          display: none;
      }
    }
  }
  
  @keyframes moveBackground {
    0% {
      transform: translate(0, 0);
    }
    25% {
      transform: translate(10vw, -5vw);
    }
    50% {
      transform: translate(-10vw, 5vw);
    }
    75% {
      transform: translate(5vw, -10vw);
    }
    100% {
      transform: translate(0, 0);
    }
  }
