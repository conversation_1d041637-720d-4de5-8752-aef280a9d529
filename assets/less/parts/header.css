header {
  position: fixed;
  padding-top: 1.273vw;
  top: 0;
  width: 100%;
  left: 0;
  z-index: 99;
}
header > .background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 17.361vw;
  pointer-events: none;
  z-index: -1;
  backdrop-filter: blur(2.894vw);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(0, 0, 0, 0);
  -webkit-mask-image: linear-gradient(#000000, rgba(0, 0, 0, 0));
  mask-image: linear-gradient(#000000, rgba(0, 0, 0, 0));
}
header .innerMenu {
  display: inline-block;
  vertical-align: middle;
  margin-left: 9.259vw;
  list-style: none;
}
header .innerMenu li {
  display: inline-block;
}
header .innerMenu a {
  display: inline-block;
  vertical-align: middle;
  padding: 0.579vw;
  cursor: pointer;
  color: #C2C2B5;
  text-decoration: none;
  transition: color 0.3s, transform 0.3s;
}
header .innerMenu a:not(:last-of-type) {
  margin-right: 1.273vw;
}
header .innerMenu a:hover {
  color: #DC461E;
}
header .col {
  display: inline-block;
  width: 75%;
  vertical-align: middle;
  position: relative;
}
header .col:last-child {
  text-align: right;
  width: 25%;
  padding-right: 7.523vw;
}
header .col .logo {
  display: inline-block;
  width: 6.481vw;
  height: 9.838vw;
  background: #C2C2B5;
  vertical-align: middle;
  position: absolute;
  top: -1.273vw;
  left: 0;
  opacity: 1;
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
header .col .logo:hover {
  opacity: 0.4;
}
header .col .logo svg {
  position: absolute;
  left: 50%;
  height: auto;
  width: 40%;
  object-fit: contain;
  bottom: 1.447vw;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
}
header .col .socials {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  z-index: 10;
}
header .col .socials.active .social {
  color: #191919;
}
header .col .socials.active .social:hover {
  color: #DC461E;
}
header .col .socials .social {
  display: inline-block;
  padding: 0.579vw;
  cursor: pointer;
  color: #C2C2B5;
  text-decoration: none;
  vertical-align: middle;
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
header .col .socials .social:hover {
  color: #DC461E;
}
header .col .button {
  display: inline-block;
  margin-left: 2.315vw;
  vertical-align: middle;
}
#menu {
  display: inline-block;
  padding: 1.157vw;
  cursor: pointer;
  position: absolute;
  right: 0;
  top: -0.694vw;
  color: #C2C2B5;
  font-size: 1.215vw;
  z-index: 9;
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
#menu .background {
  height: 0;
  opacity: 0;
  position: absolute;
  right: 0;
  top: 0;
  background: #C2C2B5;
  -webkit-transition: opacity 0.3s 0s ease-in;
  -moz-transition: opacity 0.3s 0s ease-in;
  -o-transition: opacity 0.3s 0s ease-in;
  transition: opacity 0.3s 0s ease-in;
}
#menu.active {
  color: #191919;
}
#menu.active .background {
  opacity: 1;
}
#menu.active .hamburger .border {
  background: #191919;
}
#menu.active .hamburger .border:nth-child(1) {
  -webkit-transform: translateY(0.347vw) rotate(-45deg);
  -moz-transform: translateY(0.347vw) rotate(-45deg);
  -o-transform: translateY(0.347vw) rotate(-45deg);
  -ms-transform: translateY(0.347vw) rotate(-45deg);
  transform: translateY(0.347vw) rotate(-45deg);
}
#menu.active .hamburger .border:nth-child(2) {
  width: 0%;
}
#menu.active .hamburger .border:nth-child(3) {
  -webkit-transform: translateY(-0.347vw) rotate(45deg);
  -moz-transform: translateY(-0.347vw) rotate(45deg);
  -o-transform: translateY(-0.347vw) rotate(45deg);
  -ms-transform: translateY(-0.347vw) rotate(45deg);
  transform: translateY(-0.347vw) rotate(45deg);
}
#menu.active .innerContent {
  display: block;
  width: 26.735vw;
}
#menu div,
#menu span {
  cursor: pointer;
  display: inline-block;
}
#menu .innerContent {
  display: none;
  padding: 2.315vw 0;
}
#menu .innerContent.showContent ul.hover li a {
  opacity: 0.2;
}
#menu .innerContent.showContent ul.hover li.active a {
  opacity: 1;
}
#menu .innerContent.showContent ul li {
  visibility: visible;
  opacity: 1;
  transform: translateY(0);
}
#menu .innerContent.showContent ul li:nth-child(2) {
  transition-delay: 0.15s;
}
#menu .innerContent.showContent ul li:nth-child(3) {
  transition-delay: 0.3s;
}
#menu .innerContent.showContent ul li:nth-child(4) {
  transition-delay: 0.45s;
}
#menu .innerContent.showContent ul li:nth-child(5) {
  transition-delay: 0.6s;
}
#menu .innerContent.showContent ul li:nth-child(6) {
  transition-delay: 0.75s;
}
#menu .innerContent > div {
  display: block;
}
#menu .innerContent .menu-primary-menu-container {
  padding-bottom: 1.157vw;
}
#menu .innerContent .menu-primary-menu-container ul li {
  font-size: 1.389vw;
  line-height: 1.4;
  font-family: "owners-xwide", sans-serif;
  font-weight: 500;
}
#menu .innerContent ul {
  list-style: none;
}
#menu .innerContent ul li {
  font-family: "owners", sans-serif;
  font-size: 1.215vw;
  font-weight: 600;
  padding-bottom: 0.579vw;
  line-height: 1.794vw;
  text-transform: uppercase;
  visibility: hidden;
  position: relative;
  opacity: 0;
  transform: translateY(1.157vw);
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
#menu .innerContent ul li a {
  cursor: pointer;
  color: #191919;
  text-decoration: none;
  -webkit-transition: all 0.15s ease-out;
  -moz-transition: all 0.15s ease-out;
  -o-transition: all 0.15s ease-out;
  transition: all 0.15s ease-out;
  padding-right: 2.894vw;
}
#menu .innerContent ul li a:after {
  font-family: "icomoon";
  content: '\e900';
  position: absolute;
  right: 0;
  top: -0.116vw;
  -webkit-transition: transform 0.3s 0s ease-out;
  -moz-transition: transform 0.3s 0s ease-out;
  -o-transition: transform 0.3s 0s ease-out;
  transition: transform 0.3s 0s ease-out;
}
#menu .innerContent ul li a:hover:after {
  transform: scale(0.8);
}
#menu .hamburger {
  cursor: pointer;
  margin-left: 0.81vw;
  display: inline-block;
  height: 0.81vw;
  width: 0.926vw;
}
#menu .hamburger .border {
  position: absolute;
  display: block;
  height: 2px;
  width: 100%;
  border-radius: 0.116vw;
  background: #C2C2B5;
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
#menu .hamburger .border:first-child {
  top: 0;
}
#menu .hamburger .border:nth-child(2) {
  top: 50%;
  transform: translateY(-50%);
}
#menu .hamburger .border:nth-child(3) {
  bottom: 0;
  top: auto;
}
@media all and (max-width: 1160px) {
  header > .background {
    height: 25.863vw;
    backdrop-filter: blur(4.31vw);
  }
  header .col {
    width: 66.6666%;
  }
  header .col:last-child {
    width: 33.3333%;
  }
  header .innerMenu {
    margin-left: 13.793vw;
  }
  header .innerMenu a {
    padding: 0.862vw;
  }
  header .innerMenu a:not(:last-of-type) {
    margin-right: 1.897vw;
  }
  header .col:last-child {
    padding-right: 11.207vw;
  }
  header .col .logo {
    width: 9.655vw;
    height: 14.655vw;
    top: -1.897vw;
  }
  header .col .logo svg {
    bottom: 2.155vw;
  }
  header .col .socials .social {
    padding: 0.862vw;
  }
  header .col .button {
    margin-left: 3.448vw;
  }
  #menu {
    padding: 1.724vw;
    top: -1.034vw;
    font-size: 1.81vw;
  }
  #menu.active .hamburger .border:nth-child(1) {
    -webkit-transform: translateY(0.517vw) rotate(-45deg);
    -moz-transform: translateY(0.517vw) rotate(-45deg);
    -o-transform: translateY(0.517vw) rotate(-45deg);
    -ms-transform: translateY(0.517vw) rotate(-45deg);
    transform: translateY(0.517vw) rotate(-45deg);
  }
  #menu.active .hamburger .border:nth-child(3) {
    -webkit-transform: translateY(-0.517vw) rotate(45deg);
    -moz-transform: translateY(-0.517vw) rotate(45deg);
    -o-transform: translateY(-0.517vw) rotate(45deg);
    -ms-transform: translateY(-0.517vw) rotate(45deg);
    transform: translateY(-0.517vw) rotate(45deg);
  }
  #menu.active .innerContent {
    width: 39.827vw;
  }
  #menu .innerContent {
    padding: 3.448vw 0;
  }
  #menu .innerContent .menu-primary-menu-container {
    padding-bottom: 1.724vw;
  }
  #menu .innerContent .menu-primary-menu-container ul li {
    font-size: 1.897vw;
  }
  #menu .innerContent .menu-primary-menu-container ul li a {
    padding-right: 4.31vw;
  }
  #menu .innerContent .menu-primary-menu-container ul li a:after {
    top: -0.172vw;
  }
  #menu .innerContent ul li {
    font-size: 1.81vw;
    padding-bottom: 0.862vw;
    line-height: 2.672vw;
    transform: translateY(1.724vw);
  }
  #menu .hamburger {
    margin-left: 1.207vw;
    height: 1.207vw;
    width: 1.379vw;
  }
  #menu .hamburger .border {
    border-radius: 0.172vw;
  }
}
@media all and (max-width: 580px) {
  header {
    padding-top: 4.137vw;
  }
  header > .background {
    height: 51.723vw;
    backdrop-filter: blur(8.62vw);
  }
  header .innerMenu {
    display: none;
  }
  header .col {
    width: 25%;
  }
  header .col:last-child {
    width: 75%;
    padding-right: 24.138vw;
  }
  header .col .logo {
    width: 12.069vw;
    height: 15.517vw;
    top: -8.62vw;
  }
  header .col .logo svg {
    bottom: 4.31vw;
  }
  header .col .socials .social {
    padding: 1.724vw;
  }
  header .col .socials .social:not(:last-child) {
    margin-right: 2.758vw;
  }
  header .col .button {
    margin-left: 6.897vw;
  }
  #menu {
    padding: 3.448vw;
    top: -2.069vw;
    font-size: 3.62vw;
  }
  #menu.active .hamburger .border:nth-child(1) {
    -webkit-transform: translateY(1.034vw) rotate(-45deg);
    -moz-transform: translateY(1.034vw) rotate(-45deg);
    -o-transform: translateY(1.034vw) rotate(-45deg);
    -ms-transform: translateY(1.034vw) rotate(-45deg);
    transform: translateY(1.034vw) rotate(-45deg);
  }
  #menu.active .hamburger .border:nth-child(3) {
    -webkit-transform: translateY(-1.034vw) rotate(45deg);
    -moz-transform: translateY(-1.034vw) rotate(45deg);
    -o-transform: translateY(-1.034vw) rotate(45deg);
    -ms-transform: translateY(-1.034vw) rotate(45deg);
    transform: translateY(-1.034vw) rotate(45deg);
  }
  #menu.active .innerContent {
    width: 79.655vw;
  }
  #menu .innerContent {
    padding: 6.897vw 0;
  }
  #menu .innerContent .menu-primary-menu-container {
    padding-bottom: 3.448vw;
  }
  #menu .innerContent .menu-primary-menu-container ul li {
    font-size: 4.482vw;
  }
  #menu .innerContent .menu-primary-menu-container ul li a {
    padding-right: 8.62vw;
  }
  #menu .innerContent .menu-primary-menu-container ul li a:after {
    top: -0.344vw;
  }
  #menu .innerContent ul li {
    font-size: 3.62vw;
    padding-bottom: 1.724vw;
    line-height: 8.62vw;
    transform: translateY(3.448vw);
  }
  #menu .hamburger {
    margin-left: 3.793vw;
    height: 2.414vw;
    width: 3.793vw;
  }
  #menu .hamburger .border {
    border-radius: 0.344vw;
  }
}
