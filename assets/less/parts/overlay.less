.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    .background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%; 
      background: rgba(0, 0, 0, 0.8);
    }
    .overlayContent {
      height: 100%;
      width: 100%;
        position: relative;
        text-align: center;
    }
    .overlayImage {
      align-items: center;
      justify-content: center;
      display: flex;
      height: 100%;
      width: 100%;
    }
    img {
      display: block;
      margin: auto;
      max-width: 80%;
      max-height: 80%;
    }

    .close {
        position: absolute;
        top: 20px;
        right: 30px;
        font-size: 30px;
        color: white;
        cursor: pointer;
        z-index: 2;
        .transition(.3s);
        &:hover {
          transform: rotate(45deg);
        }
    }

    .navButton{
        cursor: pointer;
        height: @vw50;
        width: @vw50;
        line-height: @vw52;
        background: @primaryColor;
        text-align: center;
        font-size: @vw22;
        color: @hardWhite;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        .transition(.3s);
        &:hover {
          background: @hardBlack;
        }
        &.prev {
          left: @vw100;
        }
        &.next {
          right: @vw100;
        }
    }
}

@media screen and (max-width: 1160px) {
  .overlay {
    .navButton {
      height: @vw50-1160;
      width: @vw50-1160;
      line-height: @vw52-1160;
      font-size: @vw22-1160;
      &.prev {
        left: @vw100-1160;
      }
      &.next {
        right: @vw100-1160;
      }
    }
  }
}

@media screen and (max-width: 580px) {
  .overlay {
    .navButton {
      height: @vw50-580;
      width: @vw50-580;
      line-height: @vw52-580;
      font-size: @vw22-580;
      &.prev {
        left: 0;
      }
      &.next {
        right: 0;
      }
    }
  }
}
