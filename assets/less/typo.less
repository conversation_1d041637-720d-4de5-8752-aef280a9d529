@import 'vw_values.less';
@import 'constants.less'; 

.hugeTitle, .bigTitle, .biggerTitle {
  &.white {
    color: @almostWhite;
  }
}

.hugeTitle {
  font-size: @vw100 + @vw100 + @vw10;
  text-decoration: none;
  font-family: "owners-xwide", sans-serif;
  font-weight: 800;
  font-style: italic;
  .transitionMore(opacity, .3s);
  &.link {
    cursor: pointer;
    color: @primaryColor;
    &:hover {
      opacity: .6;
    }
    span {
      cursor: pointer;
    }
  }
}

.bigTitle {
  font-size: @vw100 + @vw8;
  letter-spacing: -6px;
  font-family: "owners-xwide", sans-serif;
  font-weight: 800;
  font-style: normal;
  &.compact {
    font-size: @vw100 + @vw30;
    text-transform: uppercase;
    letter-spacing: 0;
    font-family: "owners-xxnarrow", sans-serif;
    font-weight: 800;
    font-style: normal;
  }
}

.signatureTitle {
  font-size: @vw95;
  line-height: 1.2;
  font-family: "Absolute Beauty", sans-serif;
  font-weight: 100;
  font-style: normal;
  &.primary {
    color: @primaryColor;
  }
  &.secondary {
    color: @secondaryColor;
  }
}

.mediumTitle {
  font-size: @vw70;
  letter-spacing: 0;
  font-family: "Technor", sans-serif;
  font-weight: 700;
  font-style: normal;
  line-height: 1;
}

.subTitle {
  font-size: @vw36;
  line-height: 1.2;
  font-family: "Technor", sans-serif;
  font-weight: 400;
  font-style: normal;
  &.primary {
    color: @primaryColor;
  }
  &.secondary {
    color: @secondaryColor;
  }
}

.tinyTitle {
  font-size: @vw16;
  line-height: 1.4;
  text-transform: uppercase;
}

.text {
  &.bigger {
    font-size: @vw22;
    text-transform: uppercase;
    p {
      font-size: @vw22;
      text-transform: uppercase;
    }
  }
  &.white {
    p {
      color: @grey;
    }
  }
  &:not(:first-child) {
    margin-top: @vw20;
  }
  p {
    line-height: 1.2;
    font-weight: 100;
    &:not(:last-child) {
      margin-bottom: @vw22;
    }
  }
}

@media all and (max-width: 1160px) {
  .hugeTitle {
    font-size: @vw72-1160;
  }

  .bigTitle {
    font-size: @vw82-1160;
    &.compact {
      font-size: @vw70-1160;
    }
  }

  .mediumTitle {
    font-size: @vw50-1160;
  }

  .subTitle {
    font-size: @vw24-1160;
  }

  .tinyTitle {
    font-size: @vw16-1160;
  }

  .text {
    &.bigger {
      font-size: @vw22-1160;
      p {
        font-size: @vw22-1160;
      }
    }
    &:not(:first-child) {
      margin-top: @vw20-1160;
    }
    p {
      &:not(:last-child) {
        margin-bottom: @vw22-1160;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .hugeTitle {
    font-size: @vw35-580;
  }

  .bigTitle {
    font-size: @vw70-580;
    &.compact {
      font-size: @vw70-580;
    }
  }

  .mediumTitle {
    font-size: @vw50-580;
  }

  .subTitle {
    font-size: @vw24-580;
  }

  .tinyTitle {
    font-size: @vw16-580;
  }

  .text {
    &.bigger {
      font-size: @vw22-580;
      p { 
        font-size: @vw22-580;
      }
    }
    &:not(:first-child) {
      margin-top: @vw20-580;
    }
    p {
      &:not(:last-child) {
        margin-bottom: @vw22-580;
      }
    }
  }
}
