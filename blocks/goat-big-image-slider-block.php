<section class="goatBigImageSliderBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <?php if (get_field("slider_images")) : ?>
        <div class="bigImageSlider">
            <div class="sliderWrapper">
                <?php while (have_rows('slider_images')) : the_row(); ?>
                    <div class="slide">
                        <?php if (get_sub_field('image')) : ?>
                            <div class="slideImage">
                                <img src="<?php echo esc_url(get_sub_field('image')['url']); ?>" alt="<?php echo esc_attr(get_sub_field('image')['alt']); ?>">
                            </div>
                        <?php endif; ?>
                        
                        <?php if (get_sub_field('title') || get_sub_field('description') || get_sub_field('button')) : ?>
                            <div class="slideContent">
                                <div class="contentWrapper">
                                    <?php if (get_sub_field('title')) : ?>
                                        <h2 class="slideTitle"><?php the_sub_field('title'); ?></h2>
                                    <?php endif; ?>
                                    
                                    <?php if (get_sub_field('description')) : ?>
                                        <div class="slideDescription"><?php the_sub_field('description'); ?></div>
                                    <?php endif; ?>
                                    
                                    <?php if (get_sub_field('button')) : ?>
                                        <div class="slideButton">
                                            <?php render_text_link_sub('button'); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endwhile; ?>
            </div>
            
            <div class="sliderControls">
                <button class="sliderArrow prev" aria-label="Previous slide">
                    <i class="icon-arrow-left"></i>
                </button>
                <button class="sliderArrow next" aria-label="Next slide">
                    <i class="icon-arrow-right"></i>
                </button>
            </div>
            
            <div class="sliderDots">
                <?php 
                $slide_count = 0;
                while (have_rows('slider_images')) : the_row(); 
                    $slide_count++;
                ?>
                    <button class="dot <?php echo $slide_count === 1 ? 'active' : ''; ?>" data-slide="<?php echo $slide_count; ?>"></button>
                <?php endwhile; ?>
            </div>
        </div>
    <?php endif; ?>
</section>
