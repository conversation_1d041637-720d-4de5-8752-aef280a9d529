<section class="goatEventsBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <div class="sectionHeader">
            <?php if (get_field("subtitle")) : ?>
                <div class="subTitle primary"><?php the_field("subtitle"); ?></div>
            <?php endif; ?>
            
            <?php if (get_field("title")) : ?>
                <h2 class="biggerTitle"><?php the_field("title"); ?></h2>
            <?php endif; ?>
            
            <?php if (get_field("description")) : ?>
                <div class="description"><?php the_field("description"); ?></div>
            <?php endif; ?>
        </div>
        
        <?php if (get_field("show_event_filters")) : ?>
            <div class="eventFilters">
                <button class="filterBtn active" data-filter="all">All Events</button>
                <button class="filterBtn" data-filter="upcoming">Upcoming</button>
                <button class="filterBtn" data-filter="past">Past Events</button>
            </div>
        <?php endif; ?>
        
        <div class="eventsGrid">
            <?php 
            $events_per_page = get_field('events_per_page') ?: 6;
            $events = get_posts(array(
                'post_type' => 'event',
                'posts_per_page' => $events_per_page,
                'post_status' => 'publish',
                'meta_key' => 'event_date',
                'orderby' => 'meta_value',
                'order' => 'DESC'
            ));
            
            if ($events) : 
                foreach ($events as $event) : 
                    $event_id = $event->ID;
                    $event_date = get_field('event_date', $event_id);
                    $is_upcoming = $event_date && strtotime($event_date) > time();
                    $event_images = get_field('event_images', $event_id);
                    $featured_image = $event_images ? $event_images[0] : null;
            ?>
                <div class="eventCard" data-category="<?php echo $is_upcoming ? 'upcoming' : 'past'; ?>">
                    <a href="<?php echo get_permalink($event_id); ?>" class="eventLink">
                        <div class="eventImage">
                            <?php if ($featured_image) : ?>
                                <img src="<?php echo esc_url($featured_image['sizes']['medium_large']); ?>" alt="<?php echo esc_attr($featured_image['alt']); ?>">
                            <?php elseif (has_post_thumbnail($event_id)) : ?>
                                <?php echo get_the_post_thumbnail($event_id, 'medium_large'); ?>
                            <?php else : ?>
                                <div class="placeholderImage">
                                    <i class="icon-calendar"></i>
                                </div>
                            <?php endif; ?>
                            
                            <div class="eventStatus">
                                <span class="statusBadge <?php echo $is_upcoming ? 'upcoming' : 'past'; ?>">
                                    <?php echo $is_upcoming ? 'Upcoming' : 'Past Event'; ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="eventInfo">
                            <h3 class="eventTitle"><?php echo get_the_title($event_id); ?></h3>
                            
                            <div class="eventMeta">
                                <?php if (get_field('event_date', $event_id)) : ?>
                                    <div class="eventDate">
                                        <i class="icon-calendar"></i>
                                        <span><?php echo get_field('event_date', $event_id); ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if (get_field('event_location', $event_id)) : ?>
                                    <div class="eventLocation">
                                        <i class="icon-location"></i>
                                        <span><?php echo get_field('event_location', $event_id); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <?php if (get_field('event_description', $event_id)) : ?>
                                <div class="eventDescription">
                                    <p><?php echo wp_trim_words(get_field('event_description', $event_id), 25); ?></p>
                                </div>
                            <?php endif; ?>
                            
                            <div class="eventFooter">
                                <div class="readMore">
                                    <span>View Event</span>
                                    <i class="icon-arrow-right"></i>
                                </div>
                                
                                <?php if ($is_upcoming && get_field('event_registration_link', $event_id)) : ?>
                                    <div class="quickRegister">
                                        <span class="registerText">Register Now</span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </a>
                </div>
            <?php 
                endforeach;
            endif; 
            ?>
        </div>
        
        <?php if (get_field("load_more_enabled")) : ?>
            <div class="loadMoreWrapper">
                <button class="button loadMoreBtn" data-post-type="event" data-posts-per-page="<?php echo $events_per_page; ?>">
                    <span class="innerText">Load More Events</span>
                    <span class="arrows">
                        <i class="icon-arrow-down"></i>
                        <i class="icon-arrow-down"></i>
                    </span>
                </button>
            </div>
        <?php endif; ?>
    </div>
</section>
