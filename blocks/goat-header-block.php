<section class="goatHeaderBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="headerBackground">
        <?php if (get_field("background_image")) : ?>
            <img src="<?php echo esc_url(get_field('background_image')['url']); ?>" alt="<?php echo esc_attr(get_field('background_image')['alt']); ?>" class="bgImage">
        <?php endif; ?>
        
        <?php if (get_field("background_overlay")) : ?>
            <div class="bgOverlay" style="background-color: <?php echo esc_attr(get_field('background_overlay')); ?>"></div>
        <?php endif; ?>
    </div>
    
    <div class="contentWrapper">
        <div class="headerContent">
            <?php if (get_field("breadcrumbs_enabled")) : ?>
                <div class="breadcrumbs">
                    <a href="<?php echo home_url(); ?>">Home</a>
                    <?php if (get_field("custom_breadcrumb")) : ?>
                        <span class="separator">/</span>
                        <span class="current"><?php the_field("custom_breadcrumb"); ?></span>
                    <?php else : ?>
                        <span class="separator">/</span>
                        <span class="current"><?php echo get_the_title(); ?></span>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            
            <?php if (get_field("subtitle")) : ?>
                <div class="subTitle primary"><?php the_field("subtitle"); ?></div>
            <?php endif; ?>
            
            <?php if (get_field("title")) : ?>
                <h1 class="pageTitle"><?php the_field("title"); ?></h1>
            <?php else : ?>
                <h1 class="pageTitle"><?php echo get_the_title(); ?></h1>
            <?php endif; ?>
            
            <?php if (get_field("description")) : ?>
                <div class="description"><?php the_field("description"); ?></div>
            <?php endif; ?>
            
            <?php if (get_field("cta_button")) : ?>
                <div class="ctaWrapper">
                    <?php render_button('cta_button'); ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>
