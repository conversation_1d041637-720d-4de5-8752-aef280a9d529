<section class="goatHomeIntroBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <div class="introContent">
            <?php if (get_field("subtitle")) : ?>
                <div class="subTitle primary"><?php the_field("subtitle"); ?></div>
            <?php endif; ?>
            
            <?php if (get_field("title")) : ?>
                <h2 class="biggerTitle"><?php the_field("title"); ?></h2>
            <?php endif; ?>
            
            <?php if (get_field("description")) : ?>
                <div class="description"><?php the_field("description"); ?></div>
            <?php endif; ?>
            
            <?php if (get_field("features")) : ?>
                <div class="featuresGrid">
                    <?php while (have_rows('features')) : the_row(); ?>
                        <div class="featureItem">
                            <?php if (get_sub_field('icon')) : ?>
                                <div class="featureIcon">
                                    <img src="<?php echo esc_url(get_sub_field('icon')['url']); ?>" alt="<?php echo esc_attr(get_sub_field('icon')['alt']); ?>">
                                </div>
                            <?php endif; ?>
                            
                            <?php if (get_sub_field('title')) : ?>
                                <h3 class="featureTitle"><?php the_sub_field('title'); ?></h3>
                            <?php endif; ?>
                            
                            <?php if (get_sub_field('description')) : ?>
                                <p class="featureDescription"><?php the_sub_field('description'); ?></p>
                            <?php endif; ?>
                        </div>
                    <?php endwhile; ?>
                </div>
            <?php endif; ?>
            
            <?php if (get_field("cta_button")) : ?>
                <div class="ctaWrapper">
                    <?php render_button('cta_button'); ?>
                </div>
            <?php endif; ?>
        </div>
        
        <?php if (get_field("side_image")) : ?>
            <div class="sideImage">
                <img src="<?php echo esc_url(get_field('side_image')['url']); ?>" alt="<?php echo esc_attr(get_field('side_image')['alt']); ?>">
            </div>
        <?php endif; ?>
    </div>
</section>
