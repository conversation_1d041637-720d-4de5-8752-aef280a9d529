<section class="goatPartnersBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <div class="sectionHeader">
            <?php if (get_field("subtitle")) : ?>
                <div class="subTitle primary"><?php the_field("subtitle"); ?></div>
            <?php endif; ?>
            
            <?php if (get_field("title")) : ?>
                <h2 class="biggerTitle"><?php the_field("title"); ?></h2>
            <?php endif; ?>
            
            <?php if (get_field("description")) : ?>
                <div class="description"><?php the_field("description"); ?></div>
            <?php endif; ?>
        </div>
        
        <div class="partnersGrid">
            <?php 
            $selected_partners = get_field('selected_partners');
            if (!$selected_partners) {
                // If no specific partners selected, show all
                $selected_partners = get_posts(array(
                    'post_type' => 'partner',
                    'posts_per_page' => -1,
                    'post_status' => 'publish'
                ));
            }
            
            if ($selected_partners) : 
                foreach ($selected_partners as $partner) : 
                    $partner_id = $partner->ID;
            ?>
                <div class="partnerCard">
                    <?php if (get_field('partner_website', $partner_id)) : ?>
                        <a href="<?php echo esc_url(get_field('partner_website', $partner_id)); ?>" target="_blank" class="partnerLink">
                    <?php endif; ?>
                    
                    <div class="partnerLogo">
                        <?php if (get_field('partner_logo', $partner_id)) : ?>
                            <img src="<?php echo esc_url(get_field('partner_logo', $partner_id)['url']); ?>" alt="<?php echo esc_attr(get_the_title($partner_id)); ?>">
                        <?php else : ?>
                            <?php echo get_the_post_thumbnail($partner_id, 'medium'); ?>
                        <?php endif; ?>
                    </div>
                    
                    <div class="partnerInfo">
                        <h3 class="partnerName"><?php echo get_the_title($partner_id); ?></h3>
                        
                        <?php if (get_field('partner_description', $partner_id)) : ?>
                            <div class="partnerDescription">
                                <p><?php echo wp_trim_words(get_field('partner_description', $partner_id), 20); ?></p>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (get_field('partner_website', $partner_id)) : ?>
                            <div class="partnerWebsite">
                                <span class="websiteLabel">Visit Website</span>
                                <i class="icon-arrow-right-up"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <?php if (get_field('partner_website', $partner_id)) : ?>
                        </a>
                    <?php endif; ?>
                </div>
            <?php 
                endforeach;
            endif; 
            ?>
        </div>
        
        <?php if (get_field("become_partner_section")) : ?>
            <div class="becomePartnerSection">
                <div class="becomePartnerContent">
                    <?php if (get_field("become_partner_title")) : ?>
                        <h3 class="becomePartnerTitle"><?php the_field("become_partner_title"); ?></h3>
                    <?php endif; ?>
                    
                    <?php if (get_field("become_partner_description")) : ?>
                        <div class="becomePartnerDescription"><?php the_field("become_partner_description"); ?></div>
                    <?php endif; ?>
                    
                    <?php if (get_field("become_partner_button")) : ?>
                        <div class="becomePartnerCTA">
                            <?php render_button('become_partner_button'); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>
