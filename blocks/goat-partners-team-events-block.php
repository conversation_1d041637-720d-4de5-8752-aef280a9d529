<section class="goatPartnersTeamEventsBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <div class="sectionHeader">
            <?php if (get_field("title")) : ?>
                <h2 class="biggerTitle"><?php the_field("title"); ?></h2>
            <?php endif; ?>
            
            <?php if (get_field("description")) : ?>
                <div class="description"><?php the_field("description"); ?></div>
            <?php endif; ?>
        </div>
        
        <div class="combinedGrid">
            <!-- Partners Section -->
            <div class="partnersSection">
                <h3 class="sectionTitle">Our Partners</h3>
                <div class="partnersGrid">
                    <?php 
                    $partners = get_posts(array(
                        'post_type' => 'partner',
                        'posts_per_page' => 6,
                        'post_status' => 'publish'
                    ));
                    
                    if ($partners) : 
                        foreach ($partners as $partner) : 
                            $partner_id = $partner->ID;
                    ?>
                        <div class="partnerCard">
                            <?php if (get_field('partner_website', $partner_id)) : ?>
                                <a href="<?php echo esc_url(get_field('partner_website', $partner_id)); ?>" target="_blank" class="partnerLink">
                            <?php endif; ?>
                            
                            <div class="partnerLogo">
                                <?php if (get_field('partner_logo', $partner_id)) : ?>
                                    <img src="<?php echo esc_url(get_field('partner_logo', $partner_id)['url']); ?>" alt="<?php echo esc_attr(get_the_title($partner_id)); ?>">
                                <?php else : ?>
                                    <?php echo get_the_post_thumbnail($partner_id, 'medium'); ?>
                                <?php endif; ?>
                            </div>
                            
                            <div class="partnerName">
                                <h4><?php echo get_the_title($partner_id); ?></h4>
                            </div>
                            
                            <?php if (get_field('partner_website', $partner_id)) : ?>
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php 
                        endforeach;
                    endif; 
                    ?>
                </div>
            </div>
            
            <!-- Team Section -->
            <div class="teamSection">
                <h3 class="sectionTitle">Featured Team Members</h3>
                <div class="teamGrid">
                    <?php 
                    $team_members = get_posts(array(
                        'post_type' => 'team_member',
                        'posts_per_page' => 3,
                        'post_status' => 'publish'
                    ));
                    
                    if ($team_members) : 
                        foreach ($team_members as $member) : 
                            $member_id = $member->ID;
                            $show_detail_page = get_field('show_detail_page', $member_id);
                    ?>
                        <div class="teamMemberCard">
                            <?php if ($show_detail_page) : ?>
                                <a href="<?php echo get_permalink($member_id); ?>" class="memberLink">
                            <?php endif; ?>
                            
                            <div class="memberImage">
                                <?php echo get_the_post_thumbnail($member_id, 'medium'); ?>
                            </div>
                            
                            <div class="memberInfo">
                                <h4 class="memberName"><?php echo get_the_title($member_id); ?></h4>
                                <?php if (get_field('member_role', $member_id)) : ?>
                                    <div class="memberRole"><?php echo get_field('member_role', $member_id); ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <?php if ($show_detail_page) : ?>
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php 
                        endforeach;
                    endif; 
                    ?>
                </div>
            </div>
            
            <!-- Events Section -->
            <div class="eventsSection">
                <h3 class="sectionTitle">Upcoming Events</h3>
                <div class="eventsGrid">
                    <?php 
                    $events = get_posts(array(
                        'post_type' => 'event',
                        'posts_per_page' => 3,
                        'post_status' => 'publish',
                        'meta_key' => 'event_date',
                        'orderby' => 'meta_value',
                        'order' => 'ASC'
                    ));
                    
                    if ($events) : 
                        foreach ($events as $event) : 
                            $event_id = $event->ID;
                    ?>
                        <div class="eventCard">
                            <a href="<?php echo get_permalink($event_id); ?>" class="eventLink">
                                <div class="eventImage">
                                    <?php echo get_the_post_thumbnail($event_id, 'medium'); ?>
                                </div>
                                
                                <div class="eventInfo">
                                    <h4 class="eventTitle"><?php echo get_the_title($event_id); ?></h4>
                                    
                                    <?php if (get_field('event_date', $event_id)) : ?>
                                        <div class="eventDate"><?php echo get_field('event_date', $event_id); ?></div>
                                    <?php endif; ?>
                                    
                                    <?php if (get_field('event_location', $event_id)) : ?>
                                        <div class="eventLocation"><?php echo get_field('event_location', $event_id); ?></div>
                                    <?php endif; ?>
                                </div>
                            </a>
                        </div>
                    <?php 
                        endforeach;
                    endif; 
                    ?>
                </div>
            </div>
        </div>
    </div>
</section>
