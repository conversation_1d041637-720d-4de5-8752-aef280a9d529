<section class="goatTeamMembersHomeBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <div class="sectionHeader">
            <?php if (get_field("subtitle")) : ?>
                <div class="subTitle primary"><?php the_field("subtitle"); ?></div>
            <?php endif; ?>
            
            <?php if (get_field("title")) : ?>
                <h2 class="biggerTitle"><?php the_field("title"); ?></h2>
            <?php endif; ?>
            
            <?php if (get_field("description")) : ?>
                <div class="description"><?php the_field("description"); ?></div>
            <?php endif; ?>
        </div>
        
        <div class="teamMembersGrid">
            <?php 
            $featured_members = get_field('featured_members');
            if (!$featured_members) {
                // If no specific members selected, show latest 6
                $featured_members = get_posts(array(
                    'post_type' => 'team_member',
                    'posts_per_page' => 6,
                    'post_status' => 'publish'
                ));
            }
            
            if ($featured_members) : 
                foreach ($featured_members as $member) : 
                    $member_id = $member->ID;
                    $show_detail_page = get_field('show_detail_page', $member_id);
            ?>
                <div class="teamMemberCard">
                    <?php if ($show_detail_page) : ?>
                        <a href="<?php echo get_permalink($member_id); ?>" class="memberLink">
                    <?php endif; ?>
                    
                    <div class="memberImage">
                        <?php echo get_the_post_thumbnail($member_id, 'medium_large'); ?>
                    </div>
                    
                    <div class="memberInfo">
                        <h3 class="memberName"><?php echo get_the_title($member_id); ?></h3>
                        
                        <?php if (get_field('member_role', $member_id)) : ?>
                            <div class="memberRole"><?php echo get_field('member_role', $member_id); ?></div>
                        <?php endif; ?>
                        
                        <?php if (get_field('member_bio', $member_id)) : ?>
                            <div class="memberBio"><?php echo wp_trim_words(get_field('member_bio', $member_id), 20); ?></div>
                        <?php endif; ?>
                        
                        <div class="memberSocials">
                            <?php 
                            $socials = array('twitch', 'tiktok', 'instagram', 'twitter', 'discord', 'facebook', 'youtube');
                            foreach ($socials as $social) :
                                if (get_field('member_' . $social, $member_id)) : ?>
                                    <a href="<?php echo esc_url(get_field('member_' . $social, $member_id)); ?>" class="socialLink" target="_blank">
                                        <i class="icon-<?php echo $social; ?>"></i>
                                    </a>
                                <?php endif;
                            endforeach; ?>
                        </div>
                    </div>
                    
                    <?php if ($show_detail_page) : ?>
                        </a>
                    <?php endif; ?>
                </div>
            <?php 
                endforeach;
            endif; 
            ?>
        </div>
        
        <?php if (get_field("view_all_button")) : ?>
            <div class="ctaWrapper">
                <?php render_button('view_all_button'); ?>
            </div>
        <?php endif; ?>
    </div>
</section>
