document.addEventListener('DOMContentLoaded', function() {
    const homeHeaderBlocks = document.querySelectorAll('.goatHomeHeaderBlock');
    
    homeHeaderBlocks.forEach(function(block) {
        initHomeHeader(block);
    });
    
    function initHomeHeader(block) {
        const scrollIndicator = block.querySelector('.scrollIndicator');
        const bgVideo = block.querySelector('.bgVideo');
        
        // Auto-play video if present
        if (bgVideo) {
            bgVideo.play().catch(function(error) {
                console.log('Video autoplay failed:', error);
            });
        }
        
        // Scroll indicator click
        if (scrollIndicator) {
            scrollIndicator.addEventListener('click', function() {
                const nextSection = block.nextElementSibling;
                if (nextSection) {
                    nextSection.scrollIntoView({ 
                        behavior: 'smooth',
                        block: 'start'
                    });
                } else {
                    window.scrollTo({
                        top: window.innerHeight,
                        behavior: 'smooth'
                    });
                }
            });
        }
        
        // Parallax effect on scroll
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            
            const bgImage = block.querySelector('.bgImage');
            const bgVideo = block.querySelector('.bgVideo');
            
            if (bgImage) {
                bgImage.style.transform = `translateY(${rate}px)`;
            }
            if (bgVideo) {
                bgVideo.style.transform = `translateY(${rate}px)`;
            }
            
            // Fade out scroll indicator
            if (scrollIndicator) {
                const opacity = Math.max(0, 1 - scrolled / 300);
                scrollIndicator.style.opacity = opacity;
            }
        });
        
        // Animate content on load
        const headerContent = block.querySelector('.headerContent');
        if (headerContent) {
            setTimeout(function() {
                headerContent.classList.add('animate-in');
            }, 500);
        }
    }
});
