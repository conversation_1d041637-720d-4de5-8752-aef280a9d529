document.addEventListener('DOMContentLoaded', function() {
    const homeIntroBlocks = document.querySelectorAll('.goatHomeIntroBlock');
    
    homeIntroBlocks.forEach(function(block) {
        initHomeIntro(block);
    });
    
    function initHomeIntro(block) {
        const features = block.querySelectorAll('.featureItem');
        
        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.2,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);
        
        // Observe features for animation
        features.forEach(function(feature, index) {
            feature.style.animationDelay = `${index * 0.2}s`;
            observer.observe(feature);
        });
        
        // Observe the entire block
        observer.observe(block);
        
        // Add hover effects to features
        features.forEach(function(feature) {
            feature.addEventListener('mouseenter', function() {
                this.classList.add('hover');
            });
            
            feature.addEventListener('mouseleave', function() {
                this.classList.remove('hover');
            });
        });
    }
});
