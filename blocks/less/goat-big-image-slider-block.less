.goatBigImageSliderBlock {
    position: relative;
    height: 70vh;
    min-height: 500px;
    overflow: hidden;
    
    @media (max-width: 768px) {
        height: 50vh;
        min-height: 400px;
    }
    
    .bigImageSlider {
        position: relative;
        width: 100%;
        height: 100%;
        
        .sliderWrapper {
            display: flex;
            width: 100%;
            height: 100%;
            transition: transform 0.6s ease-in-out;
            
            .slide {
                flex: 0 0 100%;
                position: relative;
                height: 100%;
                
                .slideImage {
                    width: 100%;
                    height: 100%;
                    
                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        object-position: center;
                    }
                }
                
                .slideContent {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: rgba(0, 0, 0, 0.4);
                    
                    .contentWrapper {
                        max-width: 800px;
                        padding: 0 20px;
                        text-align: center;
                        color: white;
                        
                        .slideTitle {
                            font-size: 3rem;
                            font-weight: bold;
                            margin-bottom: 20px;
                            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
                            
                            @media (max-width: 768px) {
                                font-size: 2rem;
                            }
                        }
                        
                        .slideDescription {
                            font-size: 1.2rem;
                            margin-bottom: 30px;
                            line-height: 1.6;
                            opacity: 0.9;
                            
                            @media (max-width: 768px) {
                                font-size: 1rem;
                            }
                        }
                        
                        .slideButton {
                            .textLink {
                                color: white;
                                text-decoration: none;
                                display: inline-flex;
                                align-items: center;
                                gap: 10px;
                                padding: 12px 25px;
                                border: 2px solid white;
                                border-radius: 50px;
                                font-weight: 600;
                                transition: all 0.3s ease;
                                
                                &:hover {
                                    background: white;
                                    color: #333;
                                    transform: translateY(-2px);
                                }
                                
                                .arrows {
                                    transition: transform 0.3s ease;
                                }
                                
                                &:hover .arrows {
                                    transform: translateX(5px);
                                }
                            }
                        }
                    }
                }
                
                // Slide animations
                &.active {
                    .slideContent .contentWrapper {
                        animation: slideContentIn 0.8s ease forwards;
                    }
                }
            }
        }
        
        .sliderControls {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 100%;
            display: flex;
            justify-content: space-between;
            padding: 0 20px;
            pointer-events: none;
            
            .sliderArrow {
                pointer-events: all;
                background: rgba(255, 255, 255, 0.2);
                border: none;
                width: 50px;
                height: 50px;
                border-radius: 50%;
                color: white;
                font-size: 18px;
                cursor: pointer;
                transition: all 0.3s ease;
                backdrop-filter: blur(10px);
                
                &:hover {
                    background: rgba(255, 255, 255, 0.3);
                    transform: scale(1.1);
                }
                
                &:active {
                    transform: scale(0.95);
                }
                
                @media (max-width: 768px) {
                    width: 40px;
                    height: 40px;
                    font-size: 14px;
                }
            }
        }
        
        .sliderDots {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
            
            .dot {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.5);
                border: none;
                cursor: pointer;
                transition: all 0.3s ease;
                
                &.active {
                    background: white;
                    transform: scale(1.2);
                }
                
                &:hover {
                    background: rgba(255, 255, 255, 0.8);
                }
            }
        }
    }
}

@keyframes slideContentIn {
    0% {
        opacity: 0;
        transform: translateY(50px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}
