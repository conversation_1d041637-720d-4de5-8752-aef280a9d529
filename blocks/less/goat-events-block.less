.goatEventsBlock {
    padding: 80px 0;
    background: #f8f9fa;
    
    .contentWrapper {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
        
        .sectionHeader {
            text-align: center;
            margin-bottom: 40px;
            
            .subTitle {
                color: #ff6b35;
                font-size: 1rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 1px;
                margin-bottom: 10px;
            }
            
            .biggerTitle {
                font-size: 2.5rem;
                font-weight: bold;
                color: #333;
                margin-bottom: 20px;
                
                @media (max-width: 768px) {
                    font-size: 2rem;
                }
            }
            
            .description {
                font-size: 1.1rem;
                color: #666;
                max-width: 600px;
                margin: 0 auto;
                line-height: 1.6;
            }
        }
        
        .eventFilters {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 50px;
            flex-wrap: wrap;
            
            .filterBtn {
                padding: 10px 20px;
                border: 2px solid #e9ecef;
                background: white;
                color: #666;
                border-radius: 25px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                
                &:hover {
                    border-color: #ff6b35;
                    color: #ff6b35;
                }
                
                &.active {
                    background: linear-gradient(45deg, #ff6b35, #f7931e);
                    border-color: #ff6b35;
                    color: white;
                }
            }
        }
        
        .eventsGrid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
            
            .eventCard {
                background: white;
                border-radius: 15px;
                overflow: hidden;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;
                
                &:hover {
                    transform: translateY(-5px);
                    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
                }
                
                &.fade-in {
                    animation: fadeIn 0.5s ease forwards;
                }
                
                .eventImage {
                    position: relative;
                    height: 200px;
                    overflow: hidden;
                    
                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        transition: transform 0.3s ease;
                    }
                    
                    .placeholderImage {
                        width: 100%;
                        height: 100%;
                        background: #f8f9fa;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: #666;
                        font-size: 2rem;
                    }
                    
                    .eventStatus {
                        position: absolute;
                        top: 15px;
                        right: 15px;
                        
                        .statusBadge {
                            padding: 5px 10px;
                            border-radius: 15px;
                            font-size: 0.8rem;
                            font-weight: 600;
                            text-transform: uppercase;
                            
                            &.upcoming {
                                background: rgba(40, 167, 69, 0.9);
                                color: white;
                            }
                            
                            &.past {
                                background: rgba(108, 117, 125, 0.9);
                                color: white;
                            }
                        }
                    }
                }
                
                .eventInfo {
                    padding: 25px;
                    
                    .eventTitle {
                        font-size: 1.3rem;
                        font-weight: bold;
                        color: #333;
                        margin-bottom: 15px;
                        line-height: 1.3;
                    }
                    
                    .eventMeta {
                        margin-bottom: 15px;
                        
                        .eventDate,
                        .eventLocation {
                            display: flex;
                            align-items: center;
                            gap: 8px;
                            color: #666;
                            margin-bottom: 8px;
                            font-size: 0.9rem;
                            
                            i {
                                color: #ff6b35;
                            }
                        }
                    }
                    
                    .eventDescription {
                        color: #666;
                        line-height: 1.5;
                        margin-bottom: 20px;
                        font-size: 0.95rem;
                    }
                    
                    .eventFooter {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        
                        .readMore {
                            display: flex;
                            align-items: center;
                            gap: 5px;
                            color: #ff6b35;
                            font-weight: 600;
                            font-size: 0.9rem;
                            transition: transform 0.3s ease;
                            
                            i {
                                font-size: 12px;
                            }
                        }
                        
                        .quickRegister {
                            .registerText {
                                background: linear-gradient(45deg, #ff6b35, #f7931e);
                                color: white;
                                padding: 5px 12px;
                                border-radius: 15px;
                                font-size: 0.8rem;
                                font-weight: 600;
                            }
                        }
                    }
                }
                
                &.animate-in {
                    animation: fadeInUp 0.6s ease forwards;
                }
            }
        }
        
        .loadMoreWrapper {
            text-align: center;
            
            .loadMoreBtn {
                background: linear-gradient(45deg, #ff6b35, #f7931e);
                color: white;
                padding: 15px 30px;
                border-radius: 50px;
                border: none;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                display: inline-flex;
                align-items: center;
                gap: 10px;
                
                &:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 10px 20px rgba(255, 107, 53, 0.3);
                }
                
                &.loading {
                    opacity: 0.7;
                    cursor: not-allowed;
                    
                    .arrows {
                        animation: spin 1s linear infinite;
                    }
                }
                
                .arrows {
                    transition: transform 0.3s ease;
                }
                
                &:hover:not(.loading) .arrows {
                    transform: translateY(3px);
                }
            }
        }
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
