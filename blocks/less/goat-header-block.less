.goatHeaderBlock {
    position: relative;
    min-height: 50vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background: #333;
    
    .headerBackground {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
        
        .bgImage {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }
        
        .bgOverlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 2;
            opacity: 0.7;
        }
    }
    
    .contentWrapper {
        position: relative;
        z-index: 3;
        text-align: center;
        color: white;
        max-width: 800px;
        padding: 0 20px;
        
        .headerContent {
            .breadcrumbs {
                margin-bottom: 20px;
                font-size: 0.9rem;
                opacity: 0.8;
                
                a {
                    color: white;
                    text-decoration: none;
                    
                    &:hover {
                        text-decoration: underline;
                    }
                }
                
                .separator {
                    margin: 0 10px;
                }
                
                .current {
                    opacity: 0.6;
                }
            }
            
            .subTitle {
                color: #ff6b35;
                font-size: 1rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 1px;
                margin-bottom: 10px;
            }
            
            .pageTitle {
                font-size: 3rem;
                font-weight: bold;
                margin-bottom: 20px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
                
                @media (max-width: 768px) {
                    font-size: 2rem;
                }
            }
            
            .description {
                font-size: 1.1rem;
                margin-bottom: 30px;
                line-height: 1.6;
                opacity: 0.9;
                
                @media (max-width: 768px) {
                    font-size: 1rem;
                }
            }
            
            .ctaWrapper {
                .button {
                    background: linear-gradient(45deg, #ff6b35, #f7931e);
                    color: white;
                    padding: 15px 30px;
                    border-radius: 50px;
                    text-decoration: none;
                    display: inline-flex;
                    align-items: center;
                    gap: 10px;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    
                    &:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 10px 20px rgba(255, 107, 53, 0.3);
                    }
                    
                    .arrows {
                        transition: transform 0.3s ease;
                    }
                    
                    &:hover .arrows {
                        transform: translateX(5px);
                    }
                }
            }
            
            &.animate-in {
                animation: fadeInUp 0.8s ease forwards;
            }
        }
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
