.goatHomeHeaderBlock {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    
    .headerBackground {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
        
        .bgImage,
        .bgVideo {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }
        
        &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.4);
            z-index: 2;
        }
    }
    
    .contentWrapper {
        position: relative;
        z-index: 3;
        text-align: center;
        color: white;
        max-width: 1200px;
        padding: 0 20px;
        
        .headerContent {
            .logoWrapper {
                margin-bottom: 2rem;
                
                .mainLogo {
                    max-width: 200px;
                    height: auto;
                    
                    @media (max-width: 768px) {
                        max-width: 150px;
                    }
                }
            }
            
            .mainTitle {
                font-size: 4rem;
                font-weight: bold;
                margin-bottom: 1rem;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
                
                @media (max-width: 768px) {
                    font-size: 2.5rem;
                }
            }
            
            .subtitle {
                font-size: 1.5rem;
                margin-bottom: 1rem;
                opacity: 0.9;
                
                @media (max-width: 768px) {
                    font-size: 1.2rem;
                }
            }
            
            .description {
                font-size: 1.1rem;
                margin-bottom: 2rem;
                max-width: 600px;
                margin-left: auto;
                margin-right: auto;
                line-height: 1.6;
                opacity: 0.8;
                
                @media (max-width: 768px) {
                    font-size: 1rem;
                }
            }
            
            .ctaWrapper {
                margin-bottom: 3rem;
                
                .button {
                    background: linear-gradient(45deg, #ff6b35, #f7931e);
                    color: white;
                    padding: 15px 30px;
                    border-radius: 50px;
                    text-decoration: none;
                    display: inline-flex;
                    align-items: center;
                    gap: 10px;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    border: none;
                    cursor: pointer;
                    
                    &:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 10px 20px rgba(255, 107, 53, 0.3);
                    }
                    
                    .arrows {
                        transition: transform 0.3s ease;
                    }
                    
                    &:hover .arrows {
                        transform: translateX(5px);
                    }
                }
            }
        }
        
        .scrollIndicator {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            color: white;
            opacity: 0.7;
            animation: bounce 2s infinite;
            
            .scrollText {
                font-size: 0.9rem;
                margin-bottom: 10px;
            }
            
            .scrollArrow {
                font-size: 1.2rem;
            }
        }
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}
