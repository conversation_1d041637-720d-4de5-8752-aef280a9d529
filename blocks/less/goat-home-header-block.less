@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

// out: false;
.goatHomeHeaderBlock {
    position: relative;
    min-height: 100vh;
    display: flex;
    overflow: hidden;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    padding-top: 0 !important;
    z-index: 2;
    &.inview {
        .svgBackground {
            opacity: 0.4;
            .transitionMore(opacity, 0.6s, 0.9s, ease-in-out);
        }
    }
    
    .headerBackground {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        &.sticky {
            position: fixed;
        }
        .bgImage,
        .bgVideo {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }
        
        &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.4);
            z-index: 2;
        }
    }

    .svgBackground {
        position: absolute;
        opacity: 0;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
        overflow: hidden;
        svg {
            width: 100%;
            height: auto;
            object-fit: cover;
            object-position: center;
            &.animate {
                path {
                    animation-name: shimmer;
                    animation-duration: @duration;
                    animation-iteration-count: infinite;
                    // animation-timing-function: ease-in-out;
                    transform-origin: center;
                    .animationStagger(@total, 0.1s);
                    .transitionMore(opacity, 0.3s);
                }
            }
        }
    }
    
    .contentWrapper {
        position: absolute;
        z-index: 3;
        text-align: left;
        bottom: @vw20;
        color: white;
        .headerContent {
            .logoWrapper {
                margin-bottom: 2rem;
                
                .mainLogo {
                    max-width: 200px;
                    height: auto;
                    
                    @media (max-width: 768px) {
                        max-width: 150px;
                    }
                }
            }
            
            .subTitle {
                margin-bottom: @vw20;
            }
            
            .ctaWrapper {
                margin-bottom: 3rem;
                
                .button {
                    background: linear-gradient(45deg, #ff6b35, #f7931e);
                    color: white;
                    padding: 15px 30px;
                    border-radius: 50px;
                    text-decoration: none;
                    display: inline-flex;
                    align-items: center;
                    gap: 10px;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    border: none;
                    cursor: pointer;
                    
                    &:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 10px 20px rgba(255, 107, 53, 0.3);
                    }
                    
                    .arrows {
                        transition: transform 0.3s ease;
                    }
                    
                    &:hover .arrows {
                        transform: translateX(5px);
                    }
                }
            }
        }
        
        .scrollIndicator {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            color: white;
            opacity: 0.7;
            animation: bounce 2s infinite;
            
            .scrollText {
                font-size: 0.9rem;
                margin-bottom: 10px;
            }
            
            .scrollArrow {
                font-size: 1.2rem;
            }
        }
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}


@total: 50; // aantal paths
@duration: 3s;
@max-delay: 5; // max delay in seconden
@base-color: rgba(0, 0, 0, 0.4);

@keyframes shimmer {
  0%, 100% {
    opacity: 0.5;
    box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  }
  50% {
    opacity: 1;
    box-shadow: 0 0 10px @base-color;
  }
}

