.goatHomeIntroBlock {
    padding: 80px 0;
    background: white;
    
    .contentWrapper {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
        display: grid;
        grid-template-columns: 1fr auto;
        gap: 60px;
        align-items: center;
        
        @media (max-width: 968px) {
            grid-template-columns: 1fr;
            gap: 40px;
        }
        
        .introContent {
            .subTitle {
                color: #ff6b35;
                font-size: 1rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 1px;
                margin-bottom: 10px;
            }
            
            .biggerTitle {
                font-size: 2.5rem;
                font-weight: bold;
                color: #333;
                margin-bottom: 20px;
                
                @media (max-width: 768px) {
                    font-size: 2rem;
                }
            }
            
            .description {
                font-size: 1.1rem;
                color: #666;
                line-height: 1.6;
                margin-bottom: 40px;
            }
            
            .featuresGrid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 30px;
                margin-bottom: 40px;
                
                .featureItem {
                    text-align: center;
                    padding: 30px 20px;
                    border-radius: 15px;
                    background: #f8f9fa;
                    transition: all 0.3s ease;
                    
                    &:hover {
                        transform: translateY(-5px);
                        background: white;
                        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
                    }
                    
                    .featureIcon {
                        margin-bottom: 20px;
                        
                        img {
                            width: 60px;
                            height: 60px;
                            object-fit: contain;
                        }
                    }
                    
                    .featureTitle {
                        font-size: 1.2rem;
                        font-weight: bold;
                        color: #333;
                        margin-bottom: 15px;
                    }
                    
                    .featureDescription {
                        color: #666;
                        line-height: 1.5;
                        font-size: 0.95rem;
                    }
                    
                    // Animation classes
                    &.animate-in {
                        animation: fadeInUp 0.6s ease forwards;
                    }
                    
                    &.hover {
                        .featureIcon img {
                            transform: scale(1.1) rotate(5deg);
                        }
                    }
                }
            }
            
            .ctaWrapper {
                .button {
                    background: linear-gradient(45deg, #ff6b35, #f7931e);
                    color: white;
                    padding: 15px 30px;
                    border-radius: 50px;
                    text-decoration: none;
                    display: inline-flex;
                    align-items: center;
                    gap: 10px;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    
                    &:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 10px 20px rgba(255, 107, 53, 0.3);
                    }
                    
                    .arrows {
                        transition: transform 0.3s ease;
                    }
                    
                    &:hover .arrows {
                        transform: translateX(5px);
                    }
                }
            }
        }
        
        .sideImage {
            max-width: 400px;
            
            @media (max-width: 968px) {
                max-width: 100%;
                text-align: center;
            }
            
            img {
                width: 100%;
                height: auto;
                border-radius: 15px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
                transition: transform 0.3s ease;
                
                &:hover {
                    transform: scale(1.02);
                }
            }
        }
    }
    
    // Animation on scroll
    &.animate-in {
        .introContent {
            animation: fadeInLeft 0.8s ease forwards;
        }
        
        .sideImage {
            animation: fadeInRight 0.8s ease 0.2s forwards;
            opacity: 0;
        }
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
