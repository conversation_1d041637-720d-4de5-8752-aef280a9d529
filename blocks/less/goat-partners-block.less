.goatPartnersBlock {
    padding: 80px 0;
    background: white;
    
    .contentWrapper {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
        
        .sectionHeader {
            text-align: center;
            margin-bottom: 60px;
            
            .subTitle {
                color: #ff6b35;
                font-size: 1rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 1px;
                margin-bottom: 10px;
            }
            
            .biggerTitle {
                font-size: 2.5rem;
                font-weight: bold;
                color: #333;
                margin-bottom: 20px;
                
                @media (max-width: 768px) {
                    font-size: 2rem;
                }
            }
            
            .description {
                font-size: 1.1rem;
                color: #666;
                max-width: 600px;
                margin: 0 auto;
                line-height: 1.6;
            }
        }
        
        .partnersGrid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
            
            @media (max-width: 768px) {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
            }
            
            .partnerCard {
                background: white;
                border: 1px solid #e9ecef;
                border-radius: 15px;
                padding: 30px;
                text-align: center;
                transition: all 0.3s ease;
                
                &:hover {
                    transform: translateY(-5px);
                    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
                    border-color: #ff6b35;
                }
                
                .partnerLink {
                    text-decoration: none;
                    color: inherit;
                    display: block;
                }
                
                .partnerLogo {
                    margin-bottom: 20px;
                    height: 80px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    
                    img {
                        max-width: 100%;
                        max-height: 100%;
                        object-fit: contain;
                        filter: grayscale(100%);
                        transition: filter 0.3s ease;
                    }
                    
                    &:hover img {
                        filter: grayscale(0%);
                    }
                }
                
                .partnerInfo {
                    .partnerName {
                        font-size: 1.2rem;
                        font-weight: bold;
                        color: #333;
                        margin-bottom: 15px;
                    }
                    
                    .partnerDescription {
                        color: #666;
                        font-size: 0.9rem;
                        line-height: 1.5;
                        margin-bottom: 15px;
                    }
                    
                    .partnerWebsite {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 5px;
                        color: #ff6b35;
                        font-size: 0.9rem;
                        font-weight: 600;
                        
                        i {
                            font-size: 12px;
                        }
                    }
                }
            }
        }
        
        .becomePartnerSection {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            border-radius: 20px;
            padding: 50px;
            text-align: center;
            color: white;
            
            @media (max-width: 768px) {
                padding: 30px 20px;
            }
            
            .becomePartnerContent {
                .becomePartnerTitle {
                    font-size: 2rem;
                    font-weight: bold;
                    margin-bottom: 20px;
                    
                    @media (max-width: 768px) {
                        font-size: 1.5rem;
                    }
                }
                
                .becomePartnerDescription {
                    font-size: 1.1rem;
                    margin-bottom: 30px;
                    opacity: 0.9;
                    max-width: 600px;
                    margin-left: auto;
                    margin-right: auto;
                    line-height: 1.6;
                }
                
                .becomePartnerCTA {
                    .button {
                        background: white;
                        color: #ff6b35;
                        padding: 15px 30px;
                        border-radius: 50px;
                        text-decoration: none;
                        display: inline-flex;
                        align-items: center;
                        gap: 10px;
                        font-weight: 600;
                        transition: all 0.3s ease;
                        
                        &:hover {
                            transform: translateY(-2px);
                            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
                        }
                        
                        .arrows {
                            transition: transform 0.3s ease;
                        }
                        
                        &:hover .arrows {
                            transform: translateX(5px);
                        }
                    }
                }
            }
        }
    }
}
