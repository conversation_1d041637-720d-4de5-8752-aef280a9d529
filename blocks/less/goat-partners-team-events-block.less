.goatPartnersTeamEventsBlock {
    padding: 80px 0;
    background: #f8f9fa;
    
    .contentWrapper {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
        
        .sectionHeader {
            text-align: center;
            margin-bottom: 60px;
            
            .biggerTitle {
                font-size: 2.5rem;
                font-weight: bold;
                color: #333;
                margin-bottom: 20px;
                
                @media (max-width: 768px) {
                    font-size: 2rem;
                }
            }
            
            .description {
                font-size: 1.1rem;
                color: #666;
                max-width: 600px;
                margin: 0 auto;
                line-height: 1.6;
            }
        }
        
        .combinedGrid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 60px;
            
            @media (min-width: 1024px) {
                grid-template-columns: 1fr 1fr;
                gap: 80px;
            }
            
            .partnersSection,
            .teamSection,
            .eventsSection {
                .sectionTitle {
                    font-size: 1.5rem;
                    font-weight: bold;
                    color: #333;
                    margin-bottom: 30px;
                    text-align: center;
                    position: relative;
                    
                    &::after {
                        content: '';
                        position: absolute;
                        bottom: -10px;
                        left: 50%;
                        transform: translateX(-50%);
                        width: 50px;
                        height: 3px;
                        background: linear-gradient(45deg, #ff6b35, #f7931e);
                        border-radius: 2px;
                    }
                }
            }
            
            .partnersSection {
                grid-column: 1 / -1;
                
                .partnersGrid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                    gap: 20px;
                    
                    .partnerCard {
                        background: white;
                        border-radius: 10px;
                        padding: 20px;
                        text-align: center;
                        transition: all 0.3s ease;
                        border: 1px solid #e9ecef;
                        
                        &:hover {
                            transform: translateY(-3px);
                            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
                        }
                        
                        .partnerLogo {
                            height: 60px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin-bottom: 15px;
                            
                            img {
                                max-width: 100%;
                                max-height: 100%;
                                object-fit: contain;
                                filter: grayscale(100%);
                                transition: filter 0.3s ease;
                            }
                        }
                        
                        .partnerName {
                            h4 {
                                font-size: 0.9rem;
                                font-weight: 600;
                                color: #333;
                                margin: 0;
                            }
                        }
                        
                        &:hover .partnerLogo img {
                            filter: grayscale(0%);
                        }
                        
                        &.animate-in {
                            animation: fadeInUp 0.6s ease forwards;
                        }
                    }
                }
            }
            
            .teamSection {
                .teamGrid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 20px;
                    
                    .teamMemberCard {
                        background: white;
                        border-radius: 15px;
                        overflow: hidden;
                        transition: all 0.3s ease;
                        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
                        
                        &:hover {
                            transform: translateY(-5px);
                            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
                        }
                        
                        .memberImage {
                            height: 150px;
                            overflow: hidden;
                            
                            img {
                                width: 100%;
                                height: 100%;
                                object-fit: cover;
                                transition: transform 0.3s ease;
                            }
                            
                            &:hover img {
                                transform: scale(1.05);
                            }
                        }
                        
                        .memberInfo {
                            padding: 15px;
                            text-align: center;
                            
                            .memberName {
                                font-size: 1rem;
                                font-weight: bold;
                                color: #333;
                                margin-bottom: 5px;
                            }
                            
                            .memberRole {
                                color: #ff6b35;
                                font-size: 0.8rem;
                                font-weight: 600;
                                text-transform: uppercase;
                                letter-spacing: 0.5px;
                            }
                        }
                        
                        &.animate-in {
                            animation: fadeInUp 0.6s ease forwards;
                        }
                    }
                }
            }
            
            .eventsSection {
                .eventsGrid {
                    display: grid;
                    grid-template-columns: 1fr;
                    gap: 20px;
                    
                    .eventCard {
                        background: white;
                        border-radius: 15px;
                        overflow: hidden;
                        transition: all 0.3s ease;
                        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
                        
                        &:hover {
                            transform: translateY(-3px);
                            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
                        }
                        
                        .eventImage {
                            height: 120px;
                            overflow: hidden;
                            
                            img {
                                width: 100%;
                                height: 100%;
                                object-fit: cover;
                                transition: transform 0.3s ease;
                            }
                            
                            &:hover img {
                                transform: scale(1.05);
                            }
                        }
                        
                        .eventInfo {
                            padding: 15px;
                            
                            .eventTitle {
                                font-size: 1rem;
                                font-weight: bold;
                                color: #333;
                                margin-bottom: 8px;
                            }
                            
                            .eventDate,
                            .eventLocation {
                                font-size: 0.8rem;
                                color: #666;
                                margin-bottom: 5px;
                                display: flex;
                                align-items: center;
                                gap: 5px;
                                
                                i {
                                    color: #ff6b35;
                                }
                            }
                        }
                        
                        &.animate-in {
                            animation: fadeInUp 0.6s ease forwards;
                        }
                    }
                }
            }
        }
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
