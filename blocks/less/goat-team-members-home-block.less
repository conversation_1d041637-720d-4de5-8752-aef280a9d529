.goatTeamMembersHomeBlock {
    padding: 80px 0;
    background: #f8f9fa;
    
    .contentWrapper {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
        
        .sectionHeader {
            text-align: center;
            margin-bottom: 60px;
            
            .subTitle {
                color: #ff6b35;
                font-size: 1rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 1px;
                margin-bottom: 10px;
            }
            
            .biggerTitle {
                font-size: 2.5rem;
                font-weight: bold;
                color: #333;
                margin-bottom: 20px;
                
                @media (max-width: 768px) {
                    font-size: 2rem;
                }
            }
            
            .description {
                font-size: 1.1rem;
                color: #666;
                max-width: 600px;
                margin: 0 auto;
                line-height: 1.6;
            }
        }
        
        .teamMembersGrid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
            
            @media (max-width: 768px) {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
            }
            
            .teamMemberCard {
                background: white;
                border-radius: 15px;
                overflow: hidden;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;
                
                &:hover {
                    transform: translateY(-5px);
                    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
                }
                
                .memberLink {
                    text-decoration: none;
                    color: inherit;
                    display: block;
                }
                
                .memberImage {
                    position: relative;
                    overflow: hidden;
                    height: 250px;
                    
                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        transition: transform 0.3s ease;
                    }
                    
                    &:hover img {
                        transform: scale(1.05);
                    }
                }
                
                .memberInfo {
                    padding: 25px;
                    
                    .memberName {
                        font-size: 1.3rem;
                        font-weight: bold;
                        color: #333;
                        margin-bottom: 8px;
                    }
                    
                    .memberRole {
                        color: #ff6b35;
                        font-weight: 600;
                        margin-bottom: 15px;
                        font-size: 0.9rem;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                    }
                    
                    .memberBio {
                        color: #666;
                        line-height: 1.5;
                        margin-bottom: 20px;
                        font-size: 0.95rem;
                    }
                    
                    .memberSocials {
                        display: flex;
                        gap: 10px;
                        flex-wrap: wrap;
                        
                        .socialLink {
                            display: inline-flex;
                            align-items: center;
                            justify-content: center;
                            width: 35px;
                            height: 35px;
                            background: #f1f3f4;
                            border-radius: 50%;
                            color: #666;
                            text-decoration: none;
                            transition: all 0.3s ease;
                            
                            &:hover {
                                background: #ff6b35;
                                color: white;
                                transform: translateY(-2px);
                            }
                            
                            i {
                                font-size: 16px;
                            }
                        }
                    }
                }
            }
        }
        
        .ctaWrapper {
            text-align: center;
            
            .button {
                background: linear-gradient(45deg, #ff6b35, #f7931e);
                color: white;
                padding: 15px 30px;
                border-radius: 50px;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                gap: 10px;
                font-weight: 600;
                transition: all 0.3s ease;
                
                &:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 10px 20px rgba(255, 107, 53, 0.3);
                }
                
                .arrows {
                    transition: transform 0.3s ease;
                }
                
                &:hover .arrows {
                    transform: translateX(5px);
                }
            }
        }
    }
}
