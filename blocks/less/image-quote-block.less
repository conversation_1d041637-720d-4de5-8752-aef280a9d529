// out: ../css/pizza-oven-block.css, compress: true, strictMath: true
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less'; 
 
.imageQuoteBlock {  
  padding-top: @vw100;
  .backgroundWrapper {  
    width: 70vw;
    position: absolute;
    z-index: -1;
    height: 70vw;
    top: 0;
    left:0;
    opacity: .8;
    .background {
      opacity: .2;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      .rounded(50%);
      height: 100%; 
      background: @grey;
      -webkit-mask-image: radial-gradient(rgba(0,0,0,1), rgba(0,0,0,0), rgba(0,0,0,0));
      mask-image: radial-gradient(rgba(0,0,0,1), rgba(0,0,0,0), rgba(0,0,0,0));
    }
  }
  &.inview {
    .buttonWrapper {
      .transform(translateY(0));
      opacity: 1;
      transition: opacity 0.45s .75s cubic-bezier(0, 0.55, 0.45, 1), transform 0.45s .75s cubic-bezier(0, 0.55, 0.45, 1);
    }
    .imageWrapper {
      opacity: 1;
      .transitionMore(opacity, .75s, .45s);
    }
  }
  .col {
      display: block;
      width: (@vw112 * 4) + (@vw13 * 3);
      margin: auto;
      position: relative;
      margin-bottom: @vw40;
  }
  .details {
    margin-top: @vw40;
  }
  .biggerTitle {
    margin-bottom: @vw60;
  }
  .text, .details {
    padding-right: @vw40;
  }
  .text {
    margin-top: 0;
  }
  .detail {
    display: block;
    &:not(:last-child) {
      margin-bottom: @vw10;
    }
    .icon {
      display: inline-block;
      vertical-align: top;
      width: @vw40;
      color: @primaryColor;
    }
    .text {
      display: inline-block;
      vertical-align: top;
      width: calc(100% ~"-" @vw40);
    }
  }
  .buttonWrapper {
    .transform(translateY(@vw30));
    opacity: 0;
  }
  .titleWrapper {
    position: absolute;
    bottom: @vw80;
    left: 20%;
    width: calc(100% ~"+" (@vw112 * 3) ~"+" (@vw13 * 4));
    .tinyTitle {
      text-align: right;
      padding-right: @vw112 + @vw16;
    }
    .mediumTitle {
      padding-left: @vw112 + @vw40;
      margin: @vw20 0 @vw50 0;
    }
  }
  .imageWrapper {
    height: auto;
    left: -20%;
    width: 100%;
    opacity: 0;
    .innerImage {
      height: 0;
      .paddingRatio(496,676);
      position: relative;
      overflow: hidden;
      video, img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
      }
    }
  }
  .buttonWrapper {
    display: block;
    text-align: left;
    .textLink {
      &:not(:last-child) {
        margin-right: @vw20;
      }
    }
  }
}

@media all and (max-width: 1160px) {
  .imageQuoteBlock {
    padding-top: @vw100-1160;
    .backgroundWrapper {
      width: 70vw;
      height: 70vw;
    }
    .col {
      width: (@vw112-1160 * 4) + (@vw13-1160 * 3);
      margin-bottom: @vw40-1160;
    }
    .details {
      margin-top: @vw40-1160;
    }
    .biggerTitle {
      margin-bottom: @vw60-1160;
    }
    .text, .details {
      padding-right: @vw40-1160;
    }
    .detail {
      &:not(:last-child) {
        margin-bottom: @vw10-1160;
      }
      .icon {
        width: @vw40-1160;
      }
      .text {
        width: calc(100% - @vw40-1160);
      }
    }
    .imageWrapper {
      left: -25%;
    }
    .buttonWrapper {
      .transform(translateY(@vw30-1160));
    }
    .titleWrapper {
      bottom: @vw80-1160;
      width: calc(100% + (@vw112-1160 * 2) + (@vw13-1160 * 2));
      .tinyTitle {
        padding-right: @vw112-1160 + @vw16-1160;
      }
      .mediumTitle {
        padding-left: @vw112-1160 + @vw40-1160;
        margin: @vw20-1160 0 @vw50-1160 0;
      }
    }
    .buttonWrapper {
      .textLink {
        &:not(:last-child) {
          margin-right: @vw20-1160;
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .imageQuoteBlock {
    padding-top: @vw100-580 + @vw50-580;
    .backgroundWrapper {
      width: 70vw;
      height: 70vw;
    }
    .col {
      width: (@vw112-580 * 4) + (@vw13-580 * 3);
      margin-bottom: @vw40-580;
    }
    .details {
      margin-top: @vw40-580;
    }
    .biggerTitle {
      margin-bottom: @vw60-580;
    }
    .text, .details {
      padding-right: @vw40-580;
    }
    .detail {
      &:not(:last-child) {
        margin-bottom: @vw10-580;
      }
      .icon {
        width: @vw40-580;
      }
      .text {
        width: calc(100% - @vw40-580);
      }
    }
    .buttonWrapper {
      .transform(translateY(@vw30-580));
    }
    .titleWrapper {
      bottom: auto;
      top: -@vw112-580;
      left: 0;
      width: 100%;
      .tinyTitle {
        padding-right: @vw112-580 + @vw16-580;
      }
      .mediumTitle {
        padding-left: @vw22-580;
        margin: @vw20-580 0 @vw50-580 0;
      }
    }
    .imageWrapper {
      left: 0;
      .innerImage {
        .paddingRatio(1,2);
      }
    }
    .buttonWrapper {
      padding-left: @vw22-580;
      .textLink {
        &:not(:last-child) {
          margin-bottom: @vw30-580;
          margin-right: 0;
        }
      }
    }
  }
}
