// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less'; 
 
.projectHeaderBlock {
  text-align: center;
  &.inview {
    .subTitle {
      opacity: 1;
      .transform(translateY(0));
    }
    .imageWrapper {
      opacity: 1;
      -webkit-filter: blur(0);
    }
  }
  .subTitle {
    opacity: 0;
    .transform(translateY(@vw20));
    transition: opacity 0.45s 0.75s ease-in-out, transform 0.45s 0.75s ease-in-out;
  }
  .imageWrapper {
    width: 100%;
    height: auto;
    object-fit: cover;
    object-position: center;
    transition: opacity 0.3s 0.03s ease-in-out, -webkit-filter 0.6s 0s ease-in-out;
    opacity: 0;
    -webkit-filter: blur(@vw50);
    .innerImage {
      .paddingRatio(2,1);
      height: 0;
      width: 100%;
      position: relative;
    }
    img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100% !important;
      height: 100% !important;
      object-fit: cover;
      object-position: center;
    }
  }
}

@media all and (max-width: 1160px) {
  .projectHeaderBlock {
    .subTitle {
      .transform(translateY(@vw20-1160));
    }
    .imageWrapper {
      -webkit-filter: blur(@vw50-1160);
    }
  }
}

@media all and (max-width: 580px) {
  .projectHeaderBlock {
    .subTitle {
      .transform(translateY(@vw20-580));
    }
    .bigTitle {
      font-size: @vw50-580;
    }
    .imageWrapper {
      -webkit-filter: blur(@vw50-580);
      .innerImage {
        .paddingRatio(1, 1.5);
      }
    }
  }
}
