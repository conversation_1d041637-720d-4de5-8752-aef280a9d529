// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.projectsBlock {
  &.inview {
    .cols {  
      .col {
        .buttonWrapper {
          .transform(translateY(0));
          opacity: 1;
          transition: opacity 0.45s .75s cubic-bezier(0, 0.55, 0.45, 1), transform 0.45s .75s cubic-bezier(0, 0.55, 0.45, 1);
        }
        .project {
          .transform(scale(1), translateY(0));
          opacity: 1;
          transition: opacity 0.75s 0s cubic-bezier(0, 0.55, 0.45, 1), transform 0.75s 0s cubic-bezier(0, 0.55, 0.45, 1);
          .stagger(2,.3s,0s);
        }
      }
    }
  }
    .cols {
        margin-left: -@vw25;
        width: calc(100% + @vw50);
        &.twoItems {
          text-align: center;
          .col {
            text-align: left;
          }
        }
    }
    .col {
        display: inline-block;
        .rounded(@vw50);
        width: calc(33.3333% - @vw50);
        margin: 0 @vw25;
        vertical-align: top;
    }
    .col {
        &:nth-child(2) {
            margin-top: @vw100;
        }
        &:nth-child(3) {
            margin-top: calc(@vw100 + @vw40);
        }
        .text {
          text-align: center;
        }
        .buttonWrapper {
          text-align: center;
          margin-top: @vw30;
          margin-bottom: @vw60;
          .transform(translateY(@vw30));
          opacity: 0;
        }
        .project {
            text-decoration: none;
            color: @almostWhite;
            cursor: pointer;
            display: block;
            width: 100%;
            .transform(scale(.8), translateY(@vw60));
            opacity: 0;
            &:hover {
              .imageWrapper {
                clip-path: inset(@vw10);
              }
              .textLink {
                color: @primaryColor;
                .arrows {
                  border-color: @primaryColor;
                  i {
                    &:first-child {
                      .transform(translate(-50%, -50%));
                    }
                    &:last-child {
                      .transform(translate(50%, -150%) scale(.5));
                    }
                  }
                }
              }
            }
            * {
              cursor: pointer;
            }
            .imageWrapper {
                margin-bottom: @vw10;
                width: 100%;
                overflow: hidden;
                position: relative;
                display: block;
                clip-path: inset(0%);
                .transitionMore(clip-path, .45s);
                .innerImage {
                  display: block;
                  .paddingRatio(1, 1);
                }
                img {
                    position: absolute;
                    pointer-events: none;
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    object-position: center;
                }
            }
            .innerCols {
              display: block;
              width: 100%;
              white-space: nowrap;
              .innerCol {
                white-space: normal;
              }
            }
            .innerCol {
                display: inline-block;
                vertical-align: middle;
                width: 40%;
                &:last-child{ 
                  width: 60%;
                }
                &:last-child {
                    text-align: right;
                }
                .tinyTitle, .category, .divider {
                  display: inline-block;
                  width: auto;
                }
                .category {
                  text-transform: uppercase;
                }
                .textLink {
                  display: inline-table;
                }
            }
        }
    }
}

@media all and (max-width: 1160px) {
  .projectsBlock {
    .cols {
      margin-left: -@vw25-1160;
      width: calc(100% + @vw50-1160);
    }
    .col {
      width: calc(33.3333% - @vw50-1160);
      margin: 0 @vw25-1160;
    }
    .col {
      &:nth-child(2) {
        margin-top: @vw100-1160;
      }
      &:nth-child(3) {
        margin-top: calc(@vw100-1160 + @vw40-1160);
      }
      .buttonWrapper {
        margin-top: @vw30-1160;
        margin-bottom: @vw60-1160;
        .transform(translateY(@vw30-1160));
      }
      .project {
        .transform(scale(.8), translateY(@vw60-1160));
        .imageWrapper {
          margin-bottom: @vw10-1160;
        }
        .innerCol {
          width: 40%;
          &:last-child {
            width: 60%;
            text-align: right;
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .projectsBlock {
    .cols {
      margin-left: -@vw25-580;
      width: calc(100% + @vw50-580);
    }
    .col {
      width: calc(100% - @vw50-580);
      margin: 0 @vw25-580;
    }
    .col {
      .transform(translate3d(0,0,0)) !important;
      &:nth-child(2) {
        margin-top: 0;
      }
      &:nth-child(3) {
        margin-top: 0;
      }
      .buttonWrapper {
        margin-top: @vw30-580;
        margin-bottom: @vw60-580;
        .transform(translateY(@vw30-580));
      }
      p {
        font-size: @vw16-580;
      }
      .project {
        .transform(scale(.8), translate3d(0,0,0), translateY(@vw60-580));
        margin-bottom: @vw60-580;
        .imageWrapper {
          margin-bottom: @vw10-580;
        }
        .innerCol {
          width: 40%;
          &:last-child {
            width: 60%;
            text-align: right;
          }
        }
      }
    }
  }
}
