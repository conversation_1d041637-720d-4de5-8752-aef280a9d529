// out: false

@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.textBlock {
  color: @almostWhite; 
  text-align: center;
  &.inview {
    .text {
      opacity: 1;
      transition: opacity 0.45s .75s cubic-bezier(0, 0.55, 0.45, 1), transform 0.45s .75s cubic-bezier(0, 0.55, 0.45, 1);
      .transform(translateY(0));
      .stagger(20, 0.05s);
    }
    .buttonWrapper {
      .textLink {
        opacity: 1;
        transition: opacity 0.45s .75s cubic-bezier(0, 0.55, 0.45, 1), transform 0.45s .75s cubic-bezier(0, 0.55, 0.45, 1);
        .transform(translateY(0));
        .stagger(20, 0.05s);
      }
    }
  }
  .text {
    opacity: 0;
    p {
      color: @almostWhite;
    }
  }
  &.center {
    text-align: center;
  }
  &.right {
    text-align: right;
  }
  .buttonWrapper {
    margin-bottom: @vw50;
    .textLink {
      display: table;
      margin: auto;
      opacity: 0;
      .transform(translateY(@vw30));
      &:not(:last-child) {
        margin-bottom: @vw22;
      }
    }
  }
}

@media all and (max-width: 1160px) {
  .textBlock {
    .buttonWrapper {
      margin-bottom: @vw50-1160;
      .textLink {
        .transform(translateY(@vw30-1160));
        &:not(:last-child) {
          margin-bottom: @vw22-1160;
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .textBlock {
    .buttonWrapper {
      margin-bottom: @vw50-580;
      .textLink {
        .transform(translateY(@vw30-580));
        &:not(:last-child) {
          margin-bottom: @vw22-580;
        }
      }
    }
  }
}