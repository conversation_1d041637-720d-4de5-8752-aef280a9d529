<?php

function jn_enqueue_assets() {
    $scripts = array(
        'Jquery' => '/libs/jquery.min.js',
        'Lenis' => '/libs/lenis.min.js',
        'Swup' => '/libs/swup.js',
        'Swup_head' => '/libs/swup_head.js',
        'Swup_Gtag' => '/libs/swup_gtag.js',
        'Select2' => '/libs/select2.min.js',
        'GSAP' => '/libs/gsap.min.js',
        'ScrollTrigger' => '/libs/ScrollTrigger.min.js',
        'Custom_Ease' => '/libs/CustomEase.min.js',
        'SPLITTEXT' => '/libs/SplitText.min.js',
        'main_js' => '/assets/js/main.js',
        'Header' => '/assets/js/header.js',
        'Menu' => '/assets/js/parts/menu.js',
        'Parallax' => '/assets/js/parts/parallax.js',
        'Gallery' => '/assets/js/parts/gallery.js',
        'Slider' => '/assets/js/parts/slider.js',
        'Split' => '/assets/js/parts/split.js',
    );

    foreach ($scripts as $handle => $path) {
        wp_enqueue_script($handle, get_theme_file_uri($path), array(), '1.0', true);
    }

    $blocks = array(
        'HOME HEADER BLOCK' => '/blocks/js/home-header-block.js',
        'TEXT SLIDER BLOCK' => '/blocks/js/text-slider-block.js',
        'GOAT HOME HEADER BLOCK' => '/blocks/js/goat-home-header-block.js',
        'GOAT HOME INTRO BLOCK' => '/blocks/js/goat-home-intro-block.js',
        'GOAT TEAM MEMBERS HOME BLOCK' => '/blocks/js/goat-team-members-home-block.js',
        'GOAT BIG IMAGE SLIDER BLOCK' => '/blocks/js/goat-big-image-slider-block.js',
        'GOAT PARTNERS TEAM EVENTS BLOCK' => '/blocks/js/goat-partners-team-events-block.js',
        'GOAT INSTAGRAM EMBED BLOCK' => '/blocks/js/goat-instagram-embed-block.js',
        'GOAT HEADER BLOCK' => '/blocks/js/goat-header-block.js',
        'GOAT PARTNERS BLOCK' => '/blocks/js/goat-partners-block.js',
        'GOAT THREE EVENTS BLOCK' => '/blocks/js/goat-three-events-block.js',
        'GOAT EVENTS BLOCK' => '/blocks/js/goat-events-block.js',
        'GOAT TEXT TWO COLUMN BLOCK' => '/blocks/js/goat-text-two-column-block.js',
        'GOAT STEPS BLOCK' => '/blocks/js/goat-steps-block.js',
        'GOAT CONTACT BLOCK' => '/blocks/js/goat-contact-block.js',
        'GOAT TEAM OVERVIEW BLOCK' => '/blocks/js/goat-team-overview-block.js',
    );

    foreach ($blocks as $handle => $path) {
        wp_enqueue_script($handle, get_theme_file_uri($path), array(), '1.0', true);
    }

    wp_enqueue_style('main', get_stylesheet_uri());
    // wp_enqueue_style('select2', get_theme_file_uri('/libs/select2.min.css'), array(), '1.1', 'all');
}

add_action('wp_enqueue_scripts', 'jn_enqueue_assets');

// Add favicon
function ilc_favicon() {
    echo "<link rel='shortcut icon' href='" . get_stylesheet_directory_uri() . "/favicon.ico' />\n";
}

add_action('wp_head', 'ilc_favicon');

// Customize theme settings
function jn_customize_register($wp_customize) {
    $sections = array(
        'customTheme-main-callout-title' => 'Title',
        'customTheme-main-callout-description' => 'Description',
        'customTheme-main-callout-featured-image' => 'Image',
        'customTheme-main-callout-noise' => 'Noise',
        'customTheme-main-callout-displacement' => 'Displacement image',
        'customTheme-main-callout-logo' => 'Logo',
        'customTheme-main-callout-logo-white' => 'Logo (White)',
        'customTheme-main-callout-telephone' => 'Telephone',
        'customTheme-main-callout-telephone-label' => 'Telephone label',
        'customTheme-main-callout-mail' => 'Mail',
        'customTheme-main-callout-address' => 'Address',
        'customTheme-main-callout-facebook' => 'Facebook URL',
        'customTheme-main-callout-linkedin' => 'LinkedIn URL',
        'customTheme-main-callout-tiktok' => 'Tiktok URL',
        'customTheme-main-callout-instagram' => 'Instagram URL',
        'customTheme-main-callout-analytics' => 'Analytics ID',
        'customTheme-main-callout-company-information' => 'Company Information'
    );

    $wp_customize->add_section('customTheme-main-callout-section', array(
        'title' => 'Main Information'
    ));

    foreach ($sections as $setting_id => $label) {
        $wp_customize->add_setting($setting_id);
        $control_args = array(
            'label' => $label,
            'section' => 'customTheme-main-callout-section',
            'settings' => $setting_id
        );

        // Voeg media-velden toe voor afbeelding, logo, noise en displacement image
        if (strpos($setting_id, 'featured-image') !== false || strpos($setting_id, 'logo') !== false || $setting_id === 'customTheme-main-callout-noise' || $setting_id === 'customTheme-main-callout-displacement') {
            $control_args['width'] = 750;
            $control_args['height'] = 500;
            $wp_customize->add_control(new WP_Customize_Media_Control($wp_customize, $setting_id . '-control', $control_args));
        } 
        // Voeg tekstgebied toe voor 'Description' en 'Company Information'
        elseif ($label === 'Description' || $label === 'Company Information') {
            $control_args['type'] = 'textarea';
            $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
        } 
        // Voeg reguliere invoervelden toe voor andere instellingen
        else {
            $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
        }
    }
}

add_action('customize_register', 'jn_customize_register');

// Register menus
function jn_register_menus() {
    register_nav_menus(array(
        'footer-menu' => 'footer-menu',
        'primary-menu' => 'Primary Menu',
    ));
}

add_action('after_setup_theme', 'jn_register_menus');

// Remove max image preview setting
add_filter('wp_robots', 'remove_max_image_preview_large', 10, 1);
function remove_max_image_preview_large($robots) {
    unset($robots['max-image-preview']);
    return $robots;
}

// blocks

add_action('acf/init', 'my_acf_blocks_init');
function my_acf_blocks_init() {

    // Check function exists.
    if( function_exists('acf_register_block_type') ) {

        // Register a testimonial block.
        acf_register_block_type(array(
            'name'              => 'home_header_block',
            'title'             => __('Home header Block'),
            'render_template'   => 'blocks/home-header-block.php',
            'category'          => 'headers',
        ));
        acf_register_block_type(array(
            'name'              => 'projects_home_block',
            'title'             => __('Projects Home Block'),
            'render_template'   => 'blocks/projects-home-block.php',
            'category'          => 'projects home block',
        ));
        acf_register_block_type(array(
            'name'              => 'projects_block',
            'title'             => __('Projects Block'),
            'render_template'   => 'blocks/projects-block.php',
            'category'          => 'projects block',
        ));
        acf_register_block_type(array(
            'name'              => 'image_quote_block',
            'title'             => __('Image Quote Block'),
            'render_template'   => 'blocks/image-quote-block.php',
            'category'          => 'image quote block',
        ));
        acf_register_block_type(array(
            'name'              => 'small_header_block',
            'title'             => __('Small header Block'),
            'render_template'   => 'blocks/small-header-block.php',
            'category'          => 'Small header block',
        ));
        acf_register_block_type(array(
            'name'              => 'text_block',
            'title'             => __('Text Block'),
            'render_template'   => 'blocks/text-block.php',
            'category'          => 'text',
        ));
        acf_register_block_type(array(
            'name'              => 'text_slider_block',
            'title'             => __('Text Slider Block'),
            'render_template'   => 'blocks/text-slider-block.php',
            'category'          => 'text',
        ));
        acf_register_block_type(array(
            'name'              => 'project_info_block',
            'title'             => __('Project Info Block'),
            'render_template'   => 'blocks/project-info-block.php',
            'category'          => 'project info block',
        ));
        acf_register_block_type(array(
            'name'              => 'project_header_block',
            'title'             => __('Project Header Block'),
            'render_template'   => 'blocks/project-header-block.php',
            'category'          => 'project header block',
        ));
        acf_register_block_type(array(
            'name'              => 'links_block',
            'title'             => __('Links Block'),
            'render_template'   => 'blocks/links-block.php',
            'category'          => 'links block',
        ));
        acf_register_block_type(array(
            'name'              => 'text_links_block',
            'title'             => __('Text Links Block'),
            'render_template'   => 'blocks/text-links-block.php',
            'category'          => 'text links block',
        ));

        // GOAT EFC Blocks
        acf_register_block_type(array(
            'name'              => 'goat_home_header_block',
            'title'             => __('GOAT Home Header Block'),
            'render_template'   => 'blocks/goat-home-header-block.php',
            'category'          => 'goat-blocks',
        ));

        acf_register_block_type(array(
            'name'              => 'goat_home_intro_block',
            'title'             => __('GOAT Home Intro Block'),
            'render_template'   => 'blocks/goat-home-intro-block.php',
            'category'          => 'goat-blocks',
        ));

        acf_register_block_type(array(
            'name'              => 'goat_team_members_home_block',
            'title'             => __('GOAT Team Members Home Block'),
            'render_template'   => 'blocks/goat-team-members-home-block.php',
            'category'          => 'goat-blocks',
        ));

        acf_register_block_type(array(
            'name'              => 'goat_big_image_slider_block',
            'title'             => __('GOAT Big Image Slider Block'),
            'render_template'   => 'blocks/goat-big-image-slider-block.php',
            'category'          => 'goat-blocks',
        ));

        acf_register_block_type(array(
            'name'              => 'goat_partners_team_events_block',
            'title'             => __('GOAT Partners Team Events Block'),
            'render_template'   => 'blocks/goat-partners-team-events-block.php',
            'category'          => 'goat-blocks',
        ));

        acf_register_block_type(array(
            'name'              => 'goat_instagram_embed_block',
            'title'             => __('GOAT Instagram Embed Block'),
            'render_template'   => 'blocks/goat-instagram-embed-block.php',
            'category'          => 'goat-blocks',
        ));

        acf_register_block_type(array(
            'name'              => 'goat_header_block',
            'title'             => __('GOAT Header Block'),
            'render_template'   => 'blocks/goat-header-block.php',
            'category'          => 'goat-blocks',
        ));

        acf_register_block_type(array(
            'name'              => 'goat_partners_block',
            'title'             => __('GOAT Partners Block'),
            'render_template'   => 'blocks/goat-partners-block.php',
            'category'          => 'goat-blocks',
        ));

        acf_register_block_type(array(
            'name'              => 'goat_three_events_block',
            'title'             => __('GOAT Three Events Block'),
            'render_template'   => 'blocks/goat-three-events-block.php',
            'category'          => 'goat-blocks',
        ));

        acf_register_block_type(array(
            'name'              => 'goat_events_block',
            'title'             => __('GOAT Events Block'),
            'render_template'   => 'blocks/goat-events-block.php',
            'category'          => 'goat-blocks',
        ));

        acf_register_block_type(array(
            'name'              => 'goat_text_two_column_block',
            'title'             => __('GOAT Text Two Column Block'),
            'render_template'   => 'blocks/goat-text-two-column-block.php',
            'category'          => 'goat-blocks',
        ));

        acf_register_block_type(array(
            'name'              => 'goat_steps_block',
            'title'             => __('GOAT Steps Block'),
            'render_template'   => 'blocks/goat-steps-block.php',
            'category'          => 'goat-blocks',
        ));

        acf_register_block_type(array(
            'name'              => 'goat_contact_block',
            'title'             => __('GOAT Contact Block'),
            'render_template'   => 'blocks/goat-contact-block.php',
            'category'          => 'goat-blocks',
        ));

        acf_register_block_type(array(
            'name'              => 'goat_team_overview_block',
            'title'             => __('GOAT Team Overview Block'),
            'render_template'   => 'blocks/goat-team-overview-block.php',
            'category'          => 'goat-blocks',
        ));
    }
}

function render_button($field_name) {
    $link = get_field($field_name);
    
    if (is_array($link) && isset($link['url'], $link['title'])) {
        $link_url = esc_url($link['url']);
        $link_title = esc_html($link['title']);
        $link_target = !empty($link['target']) ? esc_attr($link['target']) : '_self';

        echo '<a class="button" href="' . $link_url . '" title="' . esc_attr($link_title) . '">
            <span class="innerText">' . $link_title . '</span>
            <span class="arrows">
                <i class="icon-arrow-right-up"></i>
                <i class="icon-arrow-right-up"></i>
            </span>
        </a>';
    }
}


function render_text_link($field_name) {
    $link = get_field($field_name);
    if ($link) {
        $link_url = $link['url'];
        $link_title = $link['title'];
        $link_target = $link['target'] ? $link['target'] : '_self';
        echo '<a href="' . esc_url($link_url) . '" title="' . esc_html($link_title) . '" class="textLink" target="' . esc_attr($link_target) . '">
                <span class="innerText">' . esc_html($link_title) . '</span>
                <span class="arrows">
                    <i class="icon-arrow-right-up"></i>
                    <i class="icon-arrow-right-up"></i>
                </span>
              </a>';
    }
}

function render_text_link_sub($field_name) {
    $link = get_sub_field($field_name);
    if ($link) {
        $link_url = $link['url'];
        $link_title = $link['title'];
        $link_target = $link['target'] ? $link['target'] : '_self';
        echo '<a href="' . esc_url($link_url) . '" title="' . esc_html($link_title) . '" class="textLink" target="' . esc_attr($link_target) . '">
                <span class="innerText">' . esc_html($link_title) . '</span>
                <span class="arrows">
                    <i class="icon-arrow-right-up"></i>
                    <i class="icon-arrow-right-up"></i>
                </span>
              </a>';
    }
}

function custom_theme_setup() {
    add_image_size('project-thumb-mobile', 640, 800, true);
    add_image_size('project-thumb-large', 640, 800, true);
}
add_action('after_setup_theme', 'custom_theme_setup');

function get_random_projects() {
    static $random_projects = null;

    if ($random_projects === null) {
        $random_projects = new WP_Query(array(
            'posts_per_page' => 5,
            'orderby'        => 'rand',
            'post_type'      => 'project',
        ));
    }

    return $random_projects;
}

// Custom Post Types are now managed by ACF
// Import acf-post-types-goat-efc.json in WordPress Admin > Custom Fields > Tools > Import

?>

