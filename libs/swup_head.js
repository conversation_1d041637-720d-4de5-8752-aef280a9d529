(function e(t,n){if(typeof exports==="object"&&typeof module==="object")module.exports=n();else if(typeof define==="function"&&define.amd)define([],n);else if(typeof exports==="object")exports["SwupHeadPlugin"]=n();else t["SwupHeadPlugin"]=n()})(window,function(){return function(e){var t={};function n(r){if(t[r]){return t[r].exports}var o=t[r]={i:r,l:false,exports:{}};e[r].call(o.exports,o,o.exports,n);o.l=true;return o.exports}n.m=e;n.c=t;n.d=function(e,t,r){if(!n.o(e,t)){Object.defineProperty(e,t,{enumerable:true,get:r})}};n.r=function(e){if(typeof Symbol!=="undefined"&&Symbol.toStringTag){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"})}Object.defineProperty(e,"__esModule",{value:true})};n.t=function(e,t){if(t&1)e=n(e);if(t&8)return e;if(t&4&&typeof e==="object"&&e&&e.__esModule)return e;var r=Object.create(null);n.r(r);Object.defineProperty(r,"default",{enumerable:true,value:e});if(t&2&&typeof e!="string")for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r};n.n=function(e){var t=e&&e.__esModule?function t(){return e["default"]}:function t(){return e};n.d(t,"a",t);return t};n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)};n.p="";return n(n.s=0)}([function(e,t,n){"use strict";var r=n(1);var o=u(r);function u(e){return e&&e.__esModule?e:{default:e}}e.exports=o.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:true});var r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n){if(Object.prototype.hasOwnProperty.call(n,r)){e[r]=n[r]}}}return e};var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||false;r.configurable=true;if("value"in r)r.writable=true;Object.defineProperty(e,r.key,r)}}return function(t,n,r){if(n)e(t.prototype,n);if(r)e(t,r);return t}}();var u=n(2);var i=d(u);var a=n(3);var s=d(a);var l=n(4);var f=d(l);var c=n(5);var p=d(c);function d(e){return e&&e.__esModule?e:{default:e}}function v(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function h(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function m(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var y=function(e){m(t,e);function t(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};v(this,t);var n=h(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));n.name="HeadPlugin";n.assetLoadPromises=[];n.updateHead=function(){var e=n.swup.cache.getCurrentPage().originalContent;var t=(new DOMParser).parseFromString(e,"text/html");var r=(0,s.default)(document.head,t.head,{shouldPersist:n.isPersistentTag}),o=r.removed,u=r.added;var i=(0,f.default)(document.documentElement,t.documentElement);n.swup.log("Removed "+o.length+" / added "+u.length+" tags in head");if(i){n.swup.log("Updated lang attribute: "+i)}if(n.options.awaitAssets){n.assetLoadPromises=(0,p.default)(u,n.options.timeout)}else{n.assetLoadPromises=[]}t.documentElement.innerHTML="";t=null};n.isPersistentTag=function(e){var t=n.options.persistTags;if(typeof t==="function"){return t(e)}if(typeof t==="string"){return e.matches(t)}return Boolean(t)};n.options=r({persistTags:false,persistAssets:false,awaitAssets:false,timeout:3e3},e);return n}o(t,[{key:"mount",value:function e(){this.validateOptions();this.swup.on("willReplaceContent",this.updateHead);if(this.options.awaitAssets){this.originalSwupReplaceContent=this.swup.replaceContent.bind(this.swup);this.swup.replaceContent=this.replaceContentAfterAssetsLoaded.bind(this)}}},{key:"unmount",value:function e(){this.swup.off("willReplaceContent",this.updateHead);if(this.originalSwupReplaceContent){this.swup.replaceContent=this.originalSwupReplaceContent;this.originalSwupReplaceContent=null}}},{key:"validateOptions",value:function e(){if(this.options.persistAssets&&!this.options.persistTags){this.options.persistTags="link[rel=stylesheet], script[src], style"}if(this.options.awaitAssets&&!this.swup.replaceContent){this.options.awaitAssets=false;console.error("[Swup Head Plugin] Installed version of swup doesn't support awaitAssets option")}}},{key:"replaceContentAfterAssetsLoaded",value:function e(){var t=this;for(var n=arguments.length,r=Array(n),o=0;o<n;o++){r[o]=arguments[o]}if(this.assetLoadPromises.length){this.swup.log("Waiting for "+this.assetLoadPromises.length+" assets to load");return new Promise(function(e){Promise.all(t.assetLoadPromises).then(function(){t.assetLoadPromises=[];t.originalSwupReplaceContent.apply(t,r).then(e)})})}else{return this.originalSwupReplaceContent.apply(this,r)}}}]);return t}(i.default);t.default=y},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:true});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||false;r.configurable=true;if("value"in r)r.writable=true;Object.defineProperty(e,r.key,r)}}return function(t,n,r){if(n)e(t.prototype,n);if(r)e(t,r);return t}}();function o(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var u=function(){function e(){o(this,e);this.isSwupPlugin=true}r(e,[{key:"mount",value:function e(){}},{key:"unmount",value:function e(){}},{key:"_beforeMount",value:function e(){}},{key:"_afterUnmount",value:function e(){}}]);return e}();t.default=u},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.default=r;function r(e,t){var n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{},r=n.shouldPersist,a=r===undefined?function(){return false}:r;var s=Boolean(document.querySelector("[data-swup-theme]"));var l=Array.from(e.children);var f=Array.from(t.children);var c=u(l,f,{themeActive:s});var p=o(l,f);p.reverse().filter(function(e){var t=e.el;return i(t)}).filter(function(e){var t=e.el;return!a(t)}).forEach(function(t){var n=t.el;return e.removeChild(n)});c.filter(function(e){var t=e.el;return i(t)}).forEach(function(t){var n=t.el,r=t.index;e.insertBefore(n,e.children[r+1]||null)});return{removed:p.map(function(e){var t=e.el;return t}),added:c.map(function(e){var t=e.el;return t})}}function o(e,t){return e.reduce(function(e,n){var r=t.some(function(e){return a(n,e)});var o=n.matches("[data-swup-theme]");if(!r&&!o){e.push({el:n})}return e},[])}function u(e,t,n){var r=n.themeActive;return t.reduce(function(t,n,o){var u=e.some(function(e){return a(n,e)});if(!u){var i=r?o+1:o;t.push({el:n,index:i})}return t},[])}function i(e){return e.localName!=="title"}function a(e,t){return e.outerHTML===t.outerHTML}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.default=r;function r(e,t){if(e.lang!==t.lang){e.lang=t.lang;return e.lang}else{return null}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.default=i;var r=n(6);var o=u(r);function u(e){return e&&e.__esModule?e:{default:e}}function i(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;return e.filter(function(e){return e.matches("link[rel=stylesheet][href]")}).map(function(e){return(0,o.default)(e,t)})}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.default=r;function r(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;var n=function e(t){var n=t.href;return Array.from(document.styleSheets).map(function(e){var t=e.href;return t}).includes(n)};var r=function t(r){if(n(e)){r()}else{setTimeout(function(){return t(r)},10)}};return new Promise(function(e){r(e);if(t>0){setTimeout(e,t)}})}}])});